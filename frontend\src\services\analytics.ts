import api from './api';
import { ApiResponse } from '../types';

export interface OverviewStats {
  total_users: number;
  total_books: number;
  total_orders: number;
  total_revenue: number;
  new_users: number;
  new_books: number;
  new_orders: number;
  period_revenue: number;
}

export interface CategoryStats {
  id: string;
  name: string;
  book_count: number;
}

export interface RecentActivity {
  id: string;
  order_number: string;
  total_amount: number;
  status: string;
  created_at: string;
  user: {
    username: string;
    phone: string;
  };
}

export interface OverviewData {
  overview: OverviewStats;
  top_categories: CategoryStats[];
  recent_activity: RecentActivity[];
  period: string;
}

export interface SalesTrendData {
  date: string;
  order_count: number;
  revenue: number;
}

export interface UserRegistrationData {
  date: string;
  count: number;
}

export interface UserActivityData {
  date: string;
  active_users: number;
}

export interface TopUser {
  id: string;
  username: string;
  phone: string;
  order_count: number;
  total_spent: number;
}

export interface UserRetentionData {
  registration_date: string;
  registered_users: number;
  active_users: number;
}

export interface UserBehaviorData {
  user_registrations: UserRegistrationData[];
  user_activity: UserActivityData[];
  top_users: TopUser[];
  user_retention: UserRetentionData[];
  period: string;
}

export interface TopSellingBook {
  id: string;
  title: string;
  author: string;
  price: number;
  cover_image: string;
  total_sold: number;
}

export interface PriceDistribution {
  price_range: string;
  count: number;
}

export interface ConditionStats {
  condition: string;
  count: number;
}

export interface BooksAnalysisData {
  top_selling_books: TopSellingBook[];
  category_distribution: CategoryStats[];
  price_distribution: PriceDistribution[];
  condition_stats: ConditionStats[];
  most_viewed_books: TopSellingBook[];
}

export interface RevenueByCategory {
  category_name: string;
  revenue: number;
}

export interface RevenueByPaymentMethod {
  payment_method: string;
  order_count: number;
  revenue: number;
}

export interface MonthlyRevenue {
  month: string;
  revenue: number;
  order_count: number;
}

export interface AverageOrderValue {
  average_order_value: number;
  total_orders: number;
  total_revenue: number;
}

export interface RevenueAnalysisData {
  revenue_by_category: RevenueByCategory[];
  revenue_by_payment_method: RevenueByPaymentMethod[];
  monthly_revenue: MonthlyRevenue[];
  average_order_value: AverageOrderValue;
  period: string;
}

export const analyticsService = {
  // 获取平台总体统计
  async getOverview(period: string = '30d'): Promise<ApiResponse<OverviewData>> {
    const response = await api.get('/analytics/overview', {
      params: { period }
    });
    return response.data;
  },

  // 获取销售趋势
  async getSalesTrend(
    period: string = '30d',
    granularity: string = 'day'
  ): Promise<ApiResponse<{ sales_trend: SalesTrendData[]; period: string; granularity: string }>> {
    const response = await api.get('/analytics/sales-trend', {
      params: { period, granularity }
    });
    return response.data;
  },

  // 获取用户行为分析
  async getUserBehavior(period: string = '30d'): Promise<ApiResponse<UserBehaviorData>> {
    const response = await api.get('/analytics/user-behavior', {
      params: { period }
    });
    return response.data;
  },

  // 获取图书分析数据
  async getBooksAnalysis(): Promise<ApiResponse<BooksAnalysisData>> {
    const response = await api.get('/analytics/books-analysis');
    return response.data;
  },

  // 获取收入分析
  async getRevenueAnalysis(period: string = '30d'): Promise<ApiResponse<RevenueAnalysisData>> {
    const response = await api.get('/analytics/revenue-analysis', {
      params: { period }
    });
    return response.data;
  },

  // 导出数据
  async exportData(
    type: 'overview' | 'sales' | 'users' | 'books' | 'revenue',
    period: string = '30d',
    format: 'csv' | 'excel' = 'excel'
  ): Promise<Blob> {
    const response = await api.get(`/analytics/export/${type}`, {
      params: { period, format },
      responseType: 'blob'
    });
    return response.data;
  },

  // 获取实时数据
  async getRealTimeStats(): Promise<ApiResponse<{
    online_users: number;
    today_orders: number;
    today_revenue: number;
    pending_orders: number;
  }>> {
    const response = await api.get('/analytics/realtime');
    return response.data;
  }
};
