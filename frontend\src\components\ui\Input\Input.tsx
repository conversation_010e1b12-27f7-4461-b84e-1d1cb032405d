import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';
import { theme } from '../../../styles/theme';

// Input组件的属性接口
export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'prefix'> {
  size?: 'sm' | 'base' | 'lg';
  status?: 'default' | 'error' | 'success' | 'warning';
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  label?: string;
  helperText?: string;
  fullWidth?: boolean;
}

// 输入框状态样式
const inputStatus = {
  default: css`
    border-color: ${theme.colors.border};
    
    &:hover:not(:disabled) {
      border-color: ${theme.colors.gray[400]};
    }
    
    &:focus {
      border-color: ${theme.colors.primary};
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  `,
  
  error: css`
    border-color: ${theme.colors.error};
    
    &:focus {
      border-color: ${theme.colors.error};
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  `,
  
  success: css`
    border-color: ${theme.colors.success};
    
    &:focus {
      border-color: ${theme.colors.success};
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    }
  `,
  
  warning: css`
    border-color: ${theme.colors.warning};
    
    &:focus {
      border-color: ${theme.colors.warning};
      box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
    }
  `
};

// 输入框尺寸样式
const inputSizes = {
  sm: css`
    height: ${theme.sizes.input.sm.height};
    padding: ${theme.sizes.input.sm.padding};
    font-size: ${theme.sizes.input.sm.fontSize};
  `,
  
  base: css`
    height: ${theme.sizes.input.base.height};
    padding: ${theme.sizes.input.base.padding};
    font-size: ${theme.sizes.input.base.fontSize};
  `,
  
  lg: css`
    height: ${theme.sizes.input.lg.height};
    padding: ${theme.sizes.input.lg.padding};
    font-size: ${theme.sizes.input.lg.fontSize};
  `
};

// 输入框容器
const InputWrapper = styled.div<{ fullWidth?: boolean }>`
  display: inline-flex;
  flex-direction: column;
  gap: ${theme.spacing.xs};
  ${props => props.fullWidth && 'width: 100%;'}
`;

// 标签样式
const Label = styled.label`
  font-size: ${theme.typography.fontSize.sm};
  font-weight: ${theme.typography.fontWeight.medium};
  color: ${theme.colors.textPrimary};
`;

// 输入框容器（包含前缀和后缀）
const InputContainer = styled.div<{ 
  size?: 'sm' | 'base' | 'lg';
  status?: 'default' | 'error' | 'success' | 'warning';
  disabled?: boolean;
  fullWidth?: boolean;
}>`
  position: relative;
  display: inline-flex;
  align-items: center;
  background-color: ${theme.colors.white};
  border: 1px solid;
  border-radius: ${theme.borderRadius.base};
  transition: all ${theme.animation.duration.fast} ${theme.animation.easing.easeInOut};
  
  ${props => inputSizes[props.size || 'base']}
  ${props => inputStatus[props.status || 'default']}
  ${props => props.fullWidth && 'width: 100%;'}
  
  ${props => props.disabled && css`
    background-color: ${theme.colors.gray[100]};
    cursor: not-allowed;
    
    &:hover {
      border-color: ${theme.colors.border};
    }
  `}
`;

// 样式化的输入框
const StyledInput = styled.input<{ hasPrefix?: boolean; hasSuffix?: boolean }>`
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  color: ${theme.colors.textPrimary};
  
  ${props => props.hasPrefix && `padding-left: ${theme.spacing.sm};`}
  ${props => props.hasSuffix && `padding-right: ${theme.spacing.sm};`}
  
  &::placeholder {
    color: ${theme.colors.textTertiary};
  }
  
  &:disabled {
    cursor: not-allowed;
    color: ${theme.colors.textTertiary};
  }
`;

// 前缀和后缀样式
const Affix = styled.div`
  display: flex;
  align-items: center;
  color: ${theme.colors.textTertiary};
  font-size: inherit;
`;

// 帮助文本样式
const HelperText = styled.div<{ status?: 'default' | 'error' | 'success' | 'warning' }>`
  font-size: ${theme.typography.fontSize.xs};
  line-height: ${theme.typography.lineHeight.xs};
  
  ${props => {
    switch (props.status) {
      case 'error':
        return `color: ${theme.colors.error};`;
      case 'success':
        return `color: ${theme.colors.success};`;
      case 'warning':
        return `color: ${theme.colors.warning};`;
      default:
        return `color: ${theme.colors.textTertiary};`;
    }
  }}
`;

// Input组件
export const Input = forwardRef<HTMLInputElement, InputProps>(({
  size = 'base',
  status = 'default',
  prefix,
  suffix,
  label,
  helperText,
  fullWidth = false,
  disabled,
  className,
  ...props
}, ref) => {
  return (
    <InputWrapper fullWidth={fullWidth} className={className}>
      {label && <Label>{label}</Label>}
      <InputContainer
        size={size}
        status={status}
        disabled={disabled}
        fullWidth={fullWidth}
      >
        {prefix && <Affix>{prefix}</Affix>}
        <StyledInput
          ref={ref}
          hasPrefix={!!prefix}
          hasSuffix={!!suffix}
          disabled={disabled}
          {...props}
        />
        {suffix && <Affix>{suffix}</Affix>}
      </InputContainer>
      {helperText && <HelperText status={status}>{helperText}</HelperText>}
    </InputWrapper>
  );
});

Input.displayName = 'Input';

export default Input;
