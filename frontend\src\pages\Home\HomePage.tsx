import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Col,
  Card,
  Carousel,
  Typography,
  Button,
  Space,
  Tag,
  Statistic,
  List,
  Avatar,
  Rate,
  Divider,
  Skeleton
} from 'antd';
import {
  BookOutlined,
  UserOutlined,
  ShoppingOutlined,
  FireOutlined,
  StarOutlined,
  RightOutlined,
  GiftOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import EnhancedBookCard from '../../components/business/EnhancedBookCard';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';

const { Title, Text, Paragraph } = Typography;

const HomeContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  .hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('/images/book-pattern.png') repeat;
      opacity: 0.1;
    }
    
    .hero-content {
      position: relative;
      z-index: 2;
      text-align: center;
      max-width: 800px;
      margin: 0 auto;
      
      .hero-title {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 16px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        
        @media (max-width: 768px) {
          font-size: 32px;
        }
      }
      
      .hero-subtitle {
        font-size: 20px;
        margin-bottom: 32px;
        opacity: 0.9;
        
        @media (max-width: 768px) {
          font-size: 16px;
        }
      }
      
      .hero-stats {
        display: flex;
        justify-content: center;
        gap: 48px;
        margin-top: 48px;
        
        @media (max-width: 768px) {
          gap: 24px;
          flex-wrap: wrap;
        }
        
        .stat-item {
          text-align: center;
          
          .stat-number {
            font-size: 32px;
            font-weight: 700;
            display: block;
            margin-bottom: 8px;
          }
          
          .stat-label {
            font-size: 14px;
            opacity: 0.8;
          }
        }
      }
    }
  }
  
  .carousel-section {
    margin: -40px 0 40px 0;
    position: relative;
    z-index: 3;
    
    .carousel-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .ant-carousel {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        
        .carousel-item {
          height: 300px;
          background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
          display: flex !important;
          align-items: center;
          justify-content: center;
          position: relative;
          
          &.item-1 {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
          }
          
          &.item-2 {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
          }
          
          &.item-3 {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
          }
          
          .carousel-content {
            text-align: center;
            color: white;
            
            .carousel-title {
              font-size: 32px;
              font-weight: 700;
              margin-bottom: 16px;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }
            
            .carousel-desc {
              font-size: 16px;
              margin-bottom: 24px;
              opacity: 0.9;
            }
            
            .carousel-btn {
              background: rgba(255, 255, 255, 0.2);
              border: 2px solid white;
              color: white;
              border-radius: 24px;
              height: 48px;
              padding: 0 32px;
              font-weight: 600;
              backdrop-filter: blur(10px);
              
              &:hover {
                background: white;
                color: #667eea;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
              }
            }
          }
        }
      }
    }
  }
  
  .categories-section {
    padding: 60px 0;
    background: white;
    
    .section-header {
      text-align: center;
      margin-bottom: 48px;
      
      .section-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 16px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      .section-subtitle {
        font-size: 16px;
        color: #8c8c8c;
      }
    }
    
    .categories-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 24px;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .category-card {
        text-align: center;
        padding: 32px 24px;
        border-radius: 16px;
        background: white;
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
          border-color: #1677ff;
        }
        
        .category-icon {
          font-size: 48px;
          margin-bottom: 16px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        
        .category-name {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        
        .category-count {
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }
  
  .recommendations-section {
    padding: 60px 0;
    background: #fafafa;
    
    .recommendations-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
      
      .header-left {
        .section-title {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
        }
        
        .section-subtitle {
          color: #8c8c8c;
        }
      }
      
      .header-right {
        .view-more-btn {
          color: #1677ff;
          border-color: #1677ff;
          border-radius: 20px;
          
          &:hover {
            background: #1677ff;
            color: white;
            transform: translateX(4px);
          }
        }
      }
    }
    
    .books-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 24px;
    }
  }
  
  .features-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .features-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      text-align: center;
      
      .section-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 48px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }
      
      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 32px;
        
        .feature-item {
          padding: 32px 24px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          
          .feature-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #ffd700;
          }
          
          .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
          }
          
          .feature-desc {
            opacity: 0.9;
            line-height: 1.6;
          }
        }
      }
    }
  }
`;

interface HomePageProps {}

const HomePage: React.FC<HomePageProps> = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState<any[]>([]);
  const [recommendedBooks, setRecommendedBooks] = useState<any[]>([]);
  const [popularBooks, setPopularBooks] = useState<any[]>([]);
  const [newBooks, setNewBooks] = useState<any[]>([]);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setLoading(true);
      
      const [categoriesRes, recommendedRes, popularRes, newRes] = await Promise.all([
        categoriesService.getCategories(),
        booksService.getRecommendedBooks({ limit: 8 }),
        booksService.getBooks({ sort: 'sales_count_DESC', limit: 8 }),
        booksService.getBooks({ sort: 'created_at_DESC', limit: 8 })
      ]);

      if (categoriesRes.success) setCategories(categoriesRes.data.slice(0, 8));
      if (recommendedRes.success) setRecommendedBooks(recommendedRes.data);
      if (popularRes.success) setPopularBooks(popularRes.data.books);
      if (newRes.success) setNewBooks(newRes.data.books);
    } catch (error) {
      console.error('加载首页数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const carouselItems = [
    {
      title: '发现好书',
      description: '海量优质二手图书，让知识传递更有价值',
      buttonText: '立即浏览',
      action: () => navigate('/books'),
      className: 'item-1'
    },
    {
      title: '智能推荐',
      description: 'AI算法为您推荐最适合的图书',
      buttonText: '查看推荐',
      action: () => navigate('/recommendations'),
      className: 'item-2'
    },
    {
      title: '优惠促销',
      description: '限时特价，精选图书低至5折',
      buttonText: '抢购优惠',
      action: () => navigate('/promotions'),
      className: 'item-3'
    }
  ];

  const features = [
    {
      icon: <BookOutlined />,
      title: '海量图书',
      description: '涵盖各个领域的优质二手图书，满足不同读者需求'
    },
    {
      icon: <StarOutlined />,
      title: '品质保证',
      description: '严格的图书质量检测，确保每本书都物有所值'
    },
    {
      icon: <GiftOutlined />,
      title: '优惠价格',
      description: '相比新书价格更优惠，让阅读成为一种经济的享受'
    },
    {
      icon: <TrophyOutlined />,
      title: '贴心服务',
      description: '专业的客服团队，为您提供全程贴心服务'
    }
  ];

  return (
    <HomeContainer>
      {/* 英雄区域 */}
      <section className="hero-section">
        <div className="hero-content">
          <h1 className="hero-title">收书卖书</h1>
          <p className="hero-subtitle">让知识流转，让阅读更有价值</p>
          <Space size="large">
            <Button 
              type="primary" 
              size="large" 
              onClick={() => navigate('/books')}
              style={{ 
                background: 'rgba(255, 255, 255, 0.2)',
                border: '2px solid white',
                borderRadius: '24px',
                height: '48px',
                padding: '0 32px'
              }}
            >
              开始浏览
            </Button>
            <Button 
              size="large" 
              onClick={() => navigate('/sell')}
              style={{ 
                background: 'transparent',
                border: '2px solid white',
                color: 'white',
                borderRadius: '24px',
                height: '48px',
                padding: '0 32px'
              }}
            >
              出售图书
            </Button>
          </Space>
          
          <div className="hero-stats">
            <div className="stat-item">
              <span className="stat-number">10,000+</span>
              <span className="stat-label">在售图书</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">5,000+</span>
              <span className="stat-label">注册用户</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">50,000+</span>
              <span className="stat-label">成功交易</span>
            </div>
          </div>
        </div>
      </section>

      {/* 轮播图 */}
      <section className="carousel-section">
        <div className="carousel-container">
          <Carousel autoplay effect="fade">
            {carouselItems.map((item, index) => (
              <div key={index}>
                <div className={`carousel-item ${item.className}`}>
                  <div className="carousel-content">
                    <h2 className="carousel-title">{item.title}</h2>
                    <p className="carousel-desc">{item.description}</p>
                    <Button 
                      className="carousel-btn"
                      size="large"
                      onClick={item.action}
                    >
                      {item.buttonText}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </Carousel>
        </div>
      </section>

      {/* 分类导航 */}
      <section className="categories-section">
        <div className="section-header">
          <h2 className="section-title">图书分类</h2>
          <p className="section-subtitle">按分类浏览，快速找到您感兴趣的图书</p>
        </div>
        
        <div className="categories-grid">
          {loading ? (
            Array.from({ length: 8 }).map((_, index) => (
              <Card key={index} className="category-card">
                <Skeleton active />
              </Card>
            ))
          ) : (
            categories.map(category => (
              <div 
                key={category.id} 
                className="category-card"
                onClick={() => navigate(`/books?category=${category.id}`)}
              >
                <div className="category-icon">
                  <BookOutlined />
                </div>
                <div className="category-name">{category.name}</div>
                <div className="category-count">{category.book_count || 0} 本图书</div>
              </div>
            ))
          )}
        </div>
      </section>

      {/* 推荐图书 */}
      <section className="recommendations-section">
        <div className="recommendations-container">
          <div className="section-header">
            <div className="header-left">
              <h2 className="section-title">
                <FireOutlined style={{ marginRight: 8, color: '#ff4d4f' }} />
                为您推荐
              </h2>
              <p className="section-subtitle">基于您的浏览历史和偏好推荐</p>
            </div>
            <div className="header-right">
              <Button 
                className="view-more-btn"
                onClick={() => navigate('/recommendations')}
              >
                查看更多 <RightOutlined />
              </Button>
            </div>
          </div>
          
          <div className="books-grid">
            {loading ? (
              Array.from({ length: 8 }).map((_, index) => (
                <Card key={index}>
                  <Skeleton active />
                </Card>
              ))
            ) : (
              recommendedBooks.map(book => (
                <EnhancedBookCard key={book.id} book={book} />
              ))
            )}
          </div>
        </div>
      </section>

      {/* 热销图书 */}
      <section className="recommendations-section" style={{ background: 'white' }}>
        <div className="recommendations-container">
          <div className="section-header">
            <div className="header-left">
              <h2 className="section-title">
                <TrophyOutlined style={{ marginRight: 8, color: '#ffa940' }} />
                热销榜单
              </h2>
              <p className="section-subtitle">最受欢迎的图书推荐</p>
            </div>
            <div className="header-right">
              <Button 
                className="view-more-btn"
                onClick={() => navigate('/books?sort=sales_count_DESC')}
              >
                查看更多 <RightOutlined />
              </Button>
            </div>
          </div>
          
          <div className="books-grid">
            {loading ? (
              Array.from({ length: 8 }).map((_, index) => (
                <Card key={index}>
                  <Skeleton active />
                </Card>
              ))
            ) : (
              popularBooks.map(book => (
                <EnhancedBookCard key={book.id} book={book} />
              ))
            )}
          </div>
        </div>
      </section>

      {/* 新书上架 */}
      <section className="recommendations-section">
        <div className="recommendations-container">
          <div className="section-header">
            <div className="header-left">
              <h2 className="section-title">
                <StarOutlined style={{ marginRight: 8, color: '#52c41a' }} />
                新书上架
              </h2>
              <p className="section-subtitle">最新上架的优质图书</p>
            </div>
            <div className="header-right">
              <Button 
                className="view-more-btn"
                onClick={() => navigate('/books?sort=created_at_DESC')}
              >
                查看更多 <RightOutlined />
              </Button>
            </div>
          </div>
          
          <div className="books-grid">
            {loading ? (
              Array.from({ length: 8 }).map((_, index) => (
                <Card key={index}>
                  <Skeleton active />
                </Card>
              ))
            ) : (
              newBooks.map(book => (
                <EnhancedBookCard key={book.id} book={book} />
              ))
            )}
          </div>
        </div>
      </section>

      {/* 特色功能 */}
      <section className="features-section">
        <div className="features-container">
          <h2 className="section-title">为什么选择我们</h2>
          <div className="features-grid">
            {features.map((feature, index) => (
              <div key={index} className="feature-item">
                <div className="feature-icon">{feature.icon}</div>
                <h3 className="feature-title">{feature.title}</h3>
                <p className="feature-desc">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </HomeContainer>
  );
};

export default HomePage;
