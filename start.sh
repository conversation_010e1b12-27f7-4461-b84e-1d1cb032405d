#!/bin/bash

echo "启动收书卖书平台..."
echo

echo "1. 启动后端服务..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

echo "2. 等待后端服务启动..."
sleep 5

echo "3. 启动前端应用..."
cd frontend
npm start &
FRONTEND_PID=$!
cd ..

echo
echo "服务启动完成！"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:3001"
echo
echo "默认管理员账号:"
echo "超级管理员: 13800000000 / admin123456"
echo "普通管理员: 13800000001 / admin123456"
echo "测试用户: 13800000002-13800000006 / test123456"
echo
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo '正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
