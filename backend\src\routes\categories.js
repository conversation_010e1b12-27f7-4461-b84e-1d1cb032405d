const express = require('express');
const { Category } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// 获取分类列表（树形结构）
router.get('/', async (req, res) => {
  try {
    const categories = await Category.findAll({
      where: { is_active: true },
      include: [
        {
          model: Category,
          as: 'children',
          where: { is_active: true },
          required: false,
          order: [['sort_order', 'ASC']]
        }
      ],
      where: { parent_id: null },
      order: [['sort_order', 'ASC']]
    });

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取分类列表失败'
    });
  }
});

// 获取扁平化分类列表
router.get('/flat', async (req, res) => {
  try {
    const categories = await Category.findAll({
      where: { is_active: true },
      include: [
        {
          model: Category,
          as: 'parent',
          attributes: ['id', 'name']
        }
      ],
      order: [['sort_order', 'ASC']]
    });

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('获取扁平化分类列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取扁平化分类列表失败'
    });
  }
});

// 获取分类详情
router.get('/:id', async (req, res) => {
  try {
    const category = await Category.findByPk(req.params.id, {
      include: [
        {
          model: Category,
          as: 'parent',
          attributes: ['id', 'name']
        },
        {
          model: Category,
          as: 'children',
          where: { is_active: true },
          required: false,
          order: [['sort_order', 'ASC']]
        }
      ]
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    res.json({
      success: true,
      data: category
    });
  } catch (error) {
    console.error('获取分类详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取分类详情失败'
    });
  }
});

// 创建分类（需要管理员权限）
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { name, description, parent_id, sort_order } = req.body;

    // 验证父分类是否存在
    if (parent_id) {
      const parentCategory = await Category.findByPk(parent_id);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: '父分类不存在'
        });
      }
    }

    const category = await Category.create({
      name,
      description,
      parent_id,
      sort_order: sort_order || 0
    });

    res.status(201).json({
      success: true,
      message: '分类创建成功',
      data: category
    });
  } catch (error) {
    console.error('创建分类错误:', error);
    res.status(500).json({
      success: false,
      message: '创建分类失败'
    });
  }
});

// 更新分类（需要管理员权限）
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const category = await Category.findByPk(req.params.id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    const { name, description, parent_id, sort_order, is_active } = req.body;

    // 验证父分类是否存在（如果提供了parent_id）
    if (parent_id && parent_id !== category.parent_id) {
      const parentCategory = await Category.findByPk(parent_id);
      if (!parentCategory) {
        return res.status(400).json({
          success: false,
          message: '父分类不存在'
        });
      }

      // 防止循环引用
      if (parent_id === category.id) {
        return res.status(400).json({
          success: false,
          message: '不能将分类设置为自己的父分类'
        });
      }
    }

    await category.update({
      name: name || category.name,
      description: description !== undefined ? description : category.description,
      parent_id: parent_id !== undefined ? parent_id : category.parent_id,
      sort_order: sort_order !== undefined ? sort_order : category.sort_order,
      is_active: is_active !== undefined ? is_active : category.is_active
    });

    res.json({
      success: true,
      message: '分类更新成功',
      data: category
    });
  } catch (error) {
    console.error('更新分类错误:', error);
    res.status(500).json({
      success: false,
      message: '更新分类失败'
    });
  }
});

// 删除分类（需要管理员权限）
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const category = await Category.findByPk(req.params.id, {
      include: [
        {
          model: Category,
          as: 'children'
        }
      ]
    });
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 检查是否有子分类
    if (category.children && category.children.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该分类下还有子分类，无法删除'
      });
    }

    // 检查是否有关联的图书
    const { Book } = require('../models');
    const bookCount = await Book.count({
      where: { category_id: category.id }
    });

    if (bookCount > 0) {
      return res.status(400).json({
        success: false,
        message: '该分类下还有图书，无法删除'
      });
    }

    await category.destroy();

    res.json({
      success: true,
      message: '分类删除成功'
    });
  } catch (error) {
    console.error('删除分类错误:', error);
    res.status(500).json({
      success: false,
      message: '删除分类失败'
    });
  }
});

module.exports = router;
