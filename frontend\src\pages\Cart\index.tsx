import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  InputNumber,
  Image,
  Space,
  Typography,
  Checkbox,
  Empty,
  Popconfirm,
  message,
  Row,
  Col,
  Divider
} from 'antd';
import {
  DeleteOutlined,
  ShoppingOutlined,
  ClearOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useCartStore } from '../../stores/cartStore';
import { CartItem } from '../../types';

const { Title, Text } = Typography;

const CartContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const CartSummary = styled(Card)`
  position: sticky;
  top: 24px;
  
  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    &.total {
      font-size: 18px;
      font-weight: bold;
      color: #f5222d;
      border-top: 1px solid #f0f0f0;
      padding-top: 12px;
      margin-top: 12px;
    }
  }
`;

const BookInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  
  .book-image {
    width: 60px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
  }
  
  .book-details {
    flex: 1;
    
    .book-title {
      font-weight: 500;
      margin-bottom: 4px;
      color: #262626;
    }
    
    .book-author {
      color: #8c8c8c;
      font-size: 12px;
    }
  }
`;

const Cart: React.FC = () => {
  const navigate = useNavigate();
  const { items, updateQuantity, removeItem, clearCart, totalAmount } = useCartStore();
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(items.map(item => item.book_id));
    } else {
      setSelectedItems([]);
    }
  };

  // 处理单项选择
  const handleSelectItem = (bookId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, bookId]);
    } else {
      setSelectedItems(selectedItems.filter(id => id !== bookId));
    }
  };

  // 处理数量变更
  const handleQuantityChange = (bookId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(bookId);
    } else {
      updateQuantity(bookId, quantity);
    }
  };

  // 计算选中商品的总价
  const getSelectedTotal = () => {
    return items
      .filter(item => selectedItems.includes(item.book_id))
      .reduce((total, item) => total + (item.book.price * item.quantity), 0);
  };

  // 结算
  const handleCheckout = () => {
    if (selectedItems.length === 0) {
      message.warning('请选择要结算的商品');
      return;
    }

    const selectedCartItems = items.filter(item => selectedItems.includes(item.book_id));
    
    // 将选中的商品信息传递给订单页面
    navigate('/orders/create', {
      state: {
        cartItems: selectedCartItems
      }
    });
  };

  // 清空购物车
  const handleClearCart = () => {
    clearCart();
    setSelectedItems([]);
    message.success('购物车已清空');
  };

  const columns = [
    {
      title: (
        <Checkbox
          checked={selectedItems.length === items.length && items.length > 0}
          indeterminate={selectedItems.length > 0 && selectedItems.length < items.length}
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          全选
        </Checkbox>
      ),
      dataIndex: 'select',
      width: 60,
      render: (_: any, record: CartItem) => (
        <Checkbox
          checked={selectedItems.includes(record.book_id)}
          onChange={(e) => handleSelectItem(record.book_id, e.target.checked)}
        />
      )
    },
    {
      title: '商品信息',
      dataIndex: 'book',
      render: (_: any, record: CartItem) => (
        <BookInfo>
          <Image
            className="book-image"
            src={record.book.cover_image || '/images/book-placeholder.png'}
            alt={record.book.title}
            fallback="/images/book-placeholder.png"
            preview={false}
          />
          <div className="book-details">
            <div className="book-title">{record.book.title}</div>
            <div className="book-author">{record.book.author}</div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              状况: {record.book.condition} | 库存: {record.book.stock}
            </div>
          </div>
        </BookInfo>
      )
    },
    {
      title: '单价',
      dataIndex: 'price',
      width: 100,
      render: (_: any, record: CartItem) => (
        <Text strong style={{ color: '#f5222d' }}>
          ¥{record.book.price}
        </Text>
      )
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      width: 120,
      render: (_: any, record: CartItem) => (
        <InputNumber
          min={1}
          max={record.book.stock}
          value={record.quantity}
          onChange={(value) => handleQuantityChange(record.book_id, value || 1)}
          size="small"
        />
      )
    },
    {
      title: '小计',
      dataIndex: 'subtotal',
      width: 100,
      render: (_: any, record: CartItem) => (
        <Text strong style={{ color: '#f5222d' }}>
          ¥{(record.book.price * record.quantity).toFixed(2)}
        </Text>
      )
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 80,
      render: (_: any, record: CartItem) => (
        <Popconfirm
          title="确定要删除这个商品吗？"
          onConfirm={() => removeItem(record.book_id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            size="small"
          />
        </Popconfirm>
      )
    }
  ];

  if (items.length === 0) {
    return (
      <CartContainer>
        <Card>
          <Empty
            description="购物车是空的"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button
              type="primary"
              icon={<ShoppingOutlined />}
              onClick={() => navigate('/books')}
            >
              去逛逛
            </Button>
          </Empty>
        </Card>
      </CartContainer>
    );
  }

  return (
    <CartContainer>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <Title level={4} style={{ margin: 0 }}>
                  购物车 ({items.length} 件商品)
                </Title>
                <Popconfirm
                  title="确定要清空购物车吗？"
                  onConfirm={handleClearCart}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    danger
                    icon={<ClearOutlined />}
                    size="small"
                  >
                    清空购物车
                  </Button>
                </Popconfirm>
              </Space>
            }
          >
            <Table
              columns={columns}
              dataSource={items}
              rowKey="book_id"
              pagination={false}
              size="middle"
            />
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <CartSummary title="结算信息">
            <div className="summary-row">
              <span>商品总数:</span>
              <span>{selectedItems.length} 件</span>
            </div>
            <div className="summary-row">
              <span>商品总价:</span>
              <span>¥{getSelectedTotal().toFixed(2)}</span>
            </div>
            <div className="summary-row">
              <span>运费:</span>
              <span>免费</span>
            </div>
            <Divider style={{ margin: '12px 0' }} />
            <div className="summary-row total">
              <span>合计:</span>
              <span>¥{getSelectedTotal().toFixed(2)}</span>
            </div>
            
            <Button
              type="primary"
              size="large"
              block
              disabled={selectedItems.length === 0}
              onClick={handleCheckout}
              style={{ marginTop: 16 }}
            >
              结算 ({selectedItems.length})
            </Button>
            
            <Button
              block
              style={{ marginTop: 8 }}
              onClick={() => navigate('/books')}
            >
              继续购物
            </Button>
          </CartSummary>
        </Col>
      </Row>
    </CartContainer>
  );
};

export default Cart;
