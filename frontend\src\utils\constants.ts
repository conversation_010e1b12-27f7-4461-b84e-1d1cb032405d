// 图书状况选项
export const BOOK_CONDITIONS = [
  { value: '全新', label: '全新', color: 'green' },
  { value: '九成新', label: '九成新', color: 'blue' },
  { value: '八成新', label: '八成新', color: 'orange' },
  { value: '七成新', label: '七成新', color: 'gold' },
  { value: '六成新', label: '六成新', color: 'red' }
];

// 图书状态选项
export const BOOK_STATUSES = [
  { value: '上架', label: '上架', color: 'green' },
  { value: '下架', label: '下架', color: 'orange' },
  { value: '缺货', label: '缺货', color: 'red' },
  { value: '预售', label: '预售', color: 'blue' }
];

// 订单状态选项
export const ORDER_STATUSES = [
  { value: 'pending', label: '待支付', color: 'orange' },
  { value: 'paid', label: '已支付', color: 'blue' },
  { value: 'delivering', label: '配送中', color: 'cyan' },
  { value: 'delivered', label: '已送达', color: 'green' },
  { value: 'return_requested', label: '退货申请', color: 'purple' },
  { value: 'returned', label: '已退货', color: 'default' },
  { value: 'cancelled', label: '已取消', color: 'red' }
];

// 支付状态选项
export const PAYMENT_STATUSES = [
  { value: 'pending', label: '待支付', color: 'orange' },
  { value: 'paid', label: '已支付', color: 'green' },
  { value: 'failed', label: '支付失败', color: 'red' },
  { value: 'refunded', label: '已退款', color: 'purple' }
];

// 用户状态选项
export const USER_STATUSES = [
  { value: 'active', label: '正常', color: 'green' },
  { value: 'inactive', label: '未激活', color: 'orange' },
  { value: 'banned', label: '已封禁', color: 'red' }
];

// 用户角色选项
export const USER_ROLES = [
  { value: 'user', label: '普通用户', color: 'default' },
  { value: 'admin', label: '管理员', color: 'blue' },
  { value: 'super_admin', label: '超级管理员', color: 'red' }
];

// 支付方式选项
export const PAYMENT_METHODS = [
  { value: 'alipay', label: '支付宝', icon: 'alipay' },
  { value: 'wechat', label: '微信支付', icon: 'wechat' },
  { value: 'offline', label: '线下支付', icon: 'bank' }
];

// 配送方式选项
export const DELIVERY_METHODS = [
  { value: 'platform', label: '平台配送', description: '免费配送，1-3个工作日送达' },
  { value: 'pickup', label: '自提', description: '到指定地点自提' }
];

// 排序选项
export const SORT_OPTIONS = [
  { value: 'created_at_DESC', label: '最新上架' },
  { value: 'price_ASC', label: '价格从低到高' },
  { value: 'price_DESC', label: '价格从高到低' },
  { value: 'sales_count_DESC', label: '销量最高' },
  { value: 'views_DESC', label: '浏览最多' }
];

// 文件上传限制
export const UPLOAD_LIMITS = {
  avatar: {
    maxSize: 5 * 1024 * 1024, // 5MB
    accept: 'image/*',
    maxCount: 1
  },
  bookImages: {
    maxSize: 5 * 1024 * 1024, // 5MB
    accept: 'image/*',
    maxCount: 5
  }
};

// 分页配置
export const PAGINATION_CONFIG = {
  defaultPageSize: 20,
  pageSizeOptions: ['10', '20', '50', '100'],
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
};

// 正则表达式
export const REGEX_PATTERNS = {
  phone: /^1[3-9]\d{9}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  username: /^[a-zA-Z0-9_]{3,30}$/,
  isbn: /^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/
};

// 错误消息
export const ERROR_MESSAGES = {
  network: '网络连接失败，请检查网络',
  unauthorized: '登录已过期，请重新登录',
  forbidden: '权限不足',
  notFound: '请求的资源不存在',
  serverError: '服务器内部错误',
  validation: '数据验证失败',
  upload: '文件上传失败'
};

// 成功消息
export const SUCCESS_MESSAGES = {
  login: '登录成功',
  logout: '已退出登录',
  register: '注册成功',
  save: '保存成功',
  delete: '删除成功',
  update: '更新成功',
  upload: '上传成功',
  addToCart: '已添加到购物车',
  orderCreated: '订单创建成功',
  orderCancelled: '订单已取消'
};
