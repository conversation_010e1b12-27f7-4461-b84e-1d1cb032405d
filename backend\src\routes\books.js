const express = require('express');
const { Op } = require('sequelize');
const { Book, Category, User } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { validate, createBookSchema } = require('../utils/validation');

const router = express.Router();

// 获取图书列表（支持搜索、筛选、分页）
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      category_id,
      min_price,
      max_price,
      condition,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { status: '上架' };

    // 搜索条件
    if (search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { author: { [Op.iLike]: `%${search}%` } },
        { isbn: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 分类筛选
    if (category_id) {
      where.category_id = category_id;
    }

    // 价格筛选
    if (min_price) {
      where.price = { ...where.price, [Op.gte]: min_price };
    }
    if (max_price) {
      where.price = { ...where.price, [Op.lte]: max_price };
    }

    // 状况筛选
    if (condition) {
      where.condition = condition;
    }

    const { count, rows: books } = await Book.findAndCountAll({
      where,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'contact_wechat', 'contact_qq', 'contact_phone_public']
        }
      ],
      order: [[sort_by, sort_order.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        books,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取图书列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取图书列表失败'
    });
  }
});

// 获取图书详情
router.get('/:id', async (req, res) => {
  try {
    const book = await Book.findByPk(req.params.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'contact_wechat', 'contact_qq', 'contact_phone_public']
        }
      ]
    });

    if (!book) {
      return res.status(404).json({
        success: false,
        message: '图书不存在'
      });
    }

    // 增加浏览次数
    await book.increment('views');

    res.json({
      success: true,
      data: book
    });
  } catch (error) {
    console.error('获取图书详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取图书详情失败'
    });
  }
});

// 创建图书（需要管理员权限）
router.post('/', authenticateToken, requireAdmin, validate(createBookSchema), async (req, res) => {
  try {
    const bookData = {
      ...req.body,
      created_by: req.user.id
    };

    const book = await Book.create(bookData);

    // 获取完整的图书信息（包含关联数据）
    const fullBook = await Book.findByPk(book.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '图书创建成功',
      data: fullBook
    });
  } catch (error) {
    console.error('创建图书错误:', error);
    res.status(500).json({
      success: false,
      message: '创建图书失败'
    });
  }
});

// 更新图书（需要管理员权限）
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const book = await Book.findByPk(req.params.id);
    
    if (!book) {
      return res.status(404).json({
        success: false,
        message: '图书不存在'
      });
    }

    const updateData = {
      ...req.body,
      updated_by: req.user.id
    };

    await book.update(updateData);

    // 获取更新后的完整信息
    const updatedBook = await Book.findByPk(book.id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ]
    });

    res.json({
      success: true,
      message: '图书更新成功',
      data: updatedBook
    });
  } catch (error) {
    console.error('更新图书错误:', error);
    res.status(500).json({
      success: false,
      message: '更新图书失败'
    });
  }
});

// 删除图书（需要管理员权限）
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const book = await Book.findByPk(req.params.id);
    
    if (!book) {
      return res.status(404).json({
        success: false,
        message: '图书不存在'
      });
    }

    await book.destroy();

    res.json({
      success: true,
      message: '图书删除成功'
    });
  } catch (error) {
    console.error('删除图书错误:', error);
    res.status(500).json({
      success: false,
      message: '删除图书失败'
    });
  }
});

// 获取热门图书
router.get('/featured/popular', async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const books = await Book.findAll({
      where: { status: '上架' },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        }
      ],
      order: [
        ['sales_count', 'DESC'],
        ['views', 'DESC']
      ],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: books
    });
  } catch (error) {
    console.error('获取热门图书错误:', error);
    res.status(500).json({
      success: false,
      message: '获取热门图书失败'
    });
  }
});

// 获取最新图书
router.get('/featured/latest', async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    const books = await Book.findAll({
      where: { status: '上架' },
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: books
    });
  } catch (error) {
    console.error('获取最新图书错误:', error);
    res.status(500).json({
      success: false,
      message: '获取最新图书失败'
    });
  }
});

// 批量更新图书状态（需要管理员权限）
router.patch('/batch/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { book_ids, status } = req.body;

    if (!book_ids || !Array.isArray(book_ids) || book_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的图书ID列表'
      });
    }

    if (!['上架', '下架', '缺货', '预售'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }

    const [updatedCount] = await Book.update(
      {
        status,
        updated_by: req.user.id
      },
      {
        where: {
          id: {
            [Op.in]: book_ids
          }
        }
      }
    );

    res.json({
      success: true,
      message: `成功更新 ${updatedCount} 本图书的状态`,
      data: {
        updated_count: updatedCount
      }
    });
  } catch (error) {
    console.error('批量更新图书状态错误:', error);
    res.status(500).json({
      success: false,
      message: '批量更新图书状态失败'
    });
  }
});

module.exports = router;
