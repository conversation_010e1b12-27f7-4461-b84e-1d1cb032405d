import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Table,
  Tag,
  Space,
  Progress,
  List,
  Avatar,
  Spin
} from 'antd';
import {
  UserOutlined,
  BookOutlined,
  ShoppingOutlined,
  DollarOutlined,
  TrophyOutlined,
  RiseOutlined,
  FallOutlined,
  EyeOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { AdminStats } from '../../types';

const { Title, Text } = Typography;

const StatsCard = styled(Card)`
  .ant-card-body {
    padding: 20px;
  }
  
  .ant-statistic {
    .ant-statistic-title {
      color: #8c8c8c;
      font-size: 14px;
    }
    
    .ant-statistic-content {
      color: #262626;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .trend {
    margin-top: 8px;
    font-size: 12px;
    
    &.up {
      color: #52c41a;
    }
    
    &.down {
      color: #f5222d;
    }
  }
`;

const ChartCard = styled(Card)`
  .chart-content {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8c8c8c;
  }
`;

const AdminOverview: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<AdminStats | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      // 这里应该调用获取统计数据的API
      // const response = await adminService.getStats();
      // if (response.success) {
      //   setStats(response.data);
      // }
      
      // 模拟数据
      setTimeout(() => {
        setStats({
          user_stats: [
            { status: 'active', count: 1234 },
            { status: 'inactive', count: 56 },
            { status: 'banned', count: 12 }
          ],
          book_stats: [
            { status: '上架', count: 2345 },
            { status: '下架', count: 123 },
            { status: '缺货', count: 45 }
          ],
          order_stats: [
            { status: 'pending', count: 23 },
            { status: 'paid', count: 156 },
            { status: 'delivered', count: 1234 },
            { status: 'cancelled', count: 67 }
          ],
          totals: {
            users: 1302,
            books: 2513,
            orders: 1480,
            revenue: 125678.50
          },
          today: {
            users: 15,
            orders: 23,
            revenue: 1234.50
          }
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('加载统计数据失败:', error);
      setLoading(false);
    }
  };

  const recentOrders = [
    {
      id: '1',
      orderNumber: '202401150001',
      user: '张三',
      amount: 89.50,
      status: 'paid',
      time: '2024-01-15 14:30:25'
    },
    {
      id: '2',
      orderNumber: '202401150002',
      user: '李四',
      amount: 156.00,
      status: 'pending',
      time: '2024-01-15 13:45:12'
    },
    {
      id: '3',
      orderNumber: '202401150003',
      user: '王五',
      amount: 78.30,
      status: 'delivered',
      time: '2024-01-15 12:20:08'
    }
  ];

  const getStatusTag = (status: string, type: 'order' | 'user' | 'book') => {
    const configs: Record<string, Record<string, { color: string; text: string }>> = {
      order: {
        pending: { color: 'orange', text: '待支付' },
        paid: { color: 'blue', text: '已支付' },
        delivered: { color: 'green', text: '已完成' },
        cancelled: { color: 'red', text: '已取消' }
      },
      user: {
        active: { color: 'green', text: '正常' },
        inactive: { color: 'orange', text: '未激活' },
        banned: { color: 'red', text: '已封禁' }
      },
      book: {
        '上架': { color: 'green', text: '上架' },
        '下架': { color: 'orange', text: '下架' },
        '缺货': { color: 'red', text: '缺货' }
      }
    };
    
    const config = configs[type][status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '64px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>数据概览</Title>
      
      {/* 核心指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="总用户数"
              value={stats?.totals.users}
              prefix={<UserOutlined />}
              suffix={
                <div className="trend up">
                  <RiseOutlined /> +{stats?.today.users}
                </div>
              }
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="图书总数"
              value={stats?.totals.books}
              prefix={<BookOutlined />}
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="订单总数"
              value={stats?.totals.orders}
              prefix={<ShoppingOutlined />}
              suffix={
                <div className="trend up">
                  <RiseOutlined /> +{stats?.today.orders}
                </div>
              }
            />
          </StatsCard>
        </Col>
        <Col xs={12} sm={6}>
          <StatsCard>
            <Statistic
              title="总收入"
              value={stats?.totals.revenue}
              precision={2}
              prefix="¥"
              suffix={
                <div className="trend up">
                  <RiseOutlined /> +¥{stats?.today.revenue}
                </div>
              }
            />
          </StatsCard>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 用户统计 */}
        <Col xs={24} lg={8}>
          <Card title="用户状态分布" extra={<EyeOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {stats?.user_stats.map((item, index) => (
                <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Space>
                    {getStatusTag(item.status, 'user')}
                    <Text>{item.count}</Text>
                  </Space>
                  <Progress
                    percent={Math.round((item.count / (stats?.totals.users || 1)) * 100)}
                    size="small"
                    style={{ width: 100 }}
                  />
                </div>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 图书统计 */}
        <Col xs={24} lg={8}>
          <Card title="图书状态分布" extra={<BookOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {stats?.book_stats.map((item, index) => (
                <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Space>
                    {getStatusTag(item.status, 'book')}
                    <Text>{item.count}</Text>
                  </Space>
                  <Progress
                    percent={Math.round((item.count / (stats?.totals.books || 1)) * 100)}
                    size="small"
                    style={{ width: 100 }}
                  />
                </div>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 订单统计 */}
        <Col xs={24} lg={8}>
          <Card title="订单状态分布" extra={<ShoppingOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {stats?.order_stats.map((item, index) => (
                <div key={index} style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Space>
                    {getStatusTag(item.status, 'order')}
                    <Text>{item.count}</Text>
                  </Space>
                  <Progress
                    percent={Math.round((item.count / (stats?.totals.orders || 1)) * 100)}
                    size="small"
                    style={{ width: 100 }}
                  />
                </div>
              ))}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 最近订单 */}
      <Card title="最近订单" style={{ marginTop: 24 }}>
        <List
          dataSource={recentOrders}
          renderItem={(item) => (
            <List.Item
              actions={[
                <a key="view">查看详情</a>
              ]}
            >
              <List.Item.Meta
                avatar={<Avatar icon={<UserOutlined />} />}
                title={
                  <Space>
                    <Text strong>{item.orderNumber}</Text>
                    {getStatusTag(item.status, 'order')}
                  </Space>
                }
                description={
                  <Space split="·">
                    <span>用户: {item.user}</span>
                    <span>金额: ¥{item.amount}</span>
                    <span>{item.time}</span>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default AdminOverview;
