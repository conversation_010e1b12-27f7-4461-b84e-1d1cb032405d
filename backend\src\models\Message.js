const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Message = sequelize.define('Message', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  sender_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  receiver_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  book_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'books',
      key: 'id'
    }
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM('chat', 'book_inquiry', 'order_inquiry', 'system_notification'),
    defaultValue: 'chat'
  },
  status: {
    type: DataTypes.ENUM('active', 'hidden', 'deleted'),
    defaultValue: 'active'
  },
  read_status: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  is_admin_reply: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  parent_message_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'messages',
      key: 'id'
    }
  }
}, {
  tableName: 'messages'
});

// 自关联：回复关系
Message.belongsTo(Message, { 
  as: 'parent', 
  foreignKey: 'parent_message_id' 
});
Message.hasMany(Message, { 
  as: 'replies', 
  foreignKey: 'parent_message_id' 
});

module.exports = Message;
