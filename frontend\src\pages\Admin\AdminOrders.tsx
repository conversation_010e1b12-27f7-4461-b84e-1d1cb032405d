import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Typography,
  Modal,
  Form,
  message,
  Descriptions,
  Image
} from 'antd';
import {
  SearchOutlined,
  ExportOutlined,
  EyeOutlined,
  EditOutlined,
  TruckOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { Order, OrderItem } from '../../types';
import { adminService } from '../../services/admin';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const AdminOrders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const response = await adminService.getOrders({
        page: pagination.current,
        limit: pagination.pageSize
      });
      if (response.success) {
        setOrders(response.data.orders || []);
        setPagination({
          ...pagination,
          total: response.data.pagination.total_items
        });
      }
    } catch (error) {
      console.error('加载订单列表失败:', error);
      message.error('加载订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetail = (order: Order) => {
    setSelectedOrder(order);
    setDetailVisible(true);
  };

  const handleEditStatus = (order: Order) => {
    setSelectedOrder(order);
    form.setFieldsValue({
      status: order.status,
      delivery_person: order.delivery_person,
      delivery_person_phone: order.delivery_person_phone
    });
    setEditVisible(true);
  };

  const handleUpdateStatus = async (values: any) => {
    if (!selectedOrder) return;

    try {
      const response = await adminService.updateOrderStatus(selectedOrder.id, values);
      if (response.success) {
        message.success('订单状态更新成功');
        setEditVisible(false);
        loadOrders();
      }
    } catch (error) {
      console.error('更新订单状态失败:', error);
      message.error('更新订单状态失败');
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'orange', text: '待支付' },
      paid: { color: 'blue', text: '已支付' },
      delivering: { color: 'cyan', text: '配送中' },
      delivered: { color: 'green', text: '已送达' },
      return_requested: { color: 'purple', text: '退货申请' },
      returned: { color: 'default', text: '已退货' },
      cancelled: { color: 'red', text: '已取消' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getPaymentStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'orange', text: '待支付' },
      paid: { color: 'green', text: '已支付' },
      failed: { color: 'red', text: '支付失败' },
      refunded: { color: 'purple', text: '已退款' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns: ColumnsType<Order> = [
    {
      title: '订单信息',
      key: 'order',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            {record.order_number}
          </div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            {dayjs(record.created_at).format('YYYY-MM-DD HH:mm')}
          </div>
        </div>
      )
    },
    {
      title: '用户信息',
      key: 'user',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.user?.username || record.user?.phone}</div>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            {record.user?.phone}
          </div>
        </div>
      )
    },
    {
      title: '商品数量',
      key: 'items',
      width: 100,
      render: (_, record) => (
        <div>
          {record.items?.reduce((sum, item) => sum + item.quantity, 0)} 件
        </div>
      )
    },
    {
      title: '订单金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 100,
      render: (amount) => (
        <Text strong style={{ color: '#f5222d' }}>
          ¥{amount}
        </Text>
      )
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status)
    },
    {
      title: '支付状态',
      dataIndex: 'payment_status',
      key: 'payment_status',
      width: 100,
      render: (status) => getPaymentStatusTag(status)
    },
    {
      title: '配送信息',
      key: 'delivery',
      width: 150,
      render: (_, record) => (
        <div>
          {record.delivery_person && (
            <div style={{ fontSize: '12px' }}>
              配送员: {record.delivery_person}
            </div>
          )}
          {record.delivery_time && (
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              {dayjs(record.delivery_time).format('MM-DD HH:mm')}
            </div>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditStatus(record)}
          >
            状态
          </Button>
        </Space>
      )
    }
  ];

  const renderOrderItems = (items: OrderItem[]) => (
    <div>
      {items.map((item, index) => (
        <div key={index} style={{
          display: 'flex',
          alignItems: 'center',
          padding: '8px 0',
          borderBottom: index < items.length - 1 ? '1px solid #f0f0f0' : 'none'
        }}>
          <Image
            width={50}
            height={60}
            src={item.book?.cover_image || '/images/book-placeholder.png'}
            alt={item.book?.title}
            fallback="/images/book-placeholder.png"
            style={{ marginRight: 12, borderRadius: 4 }}
          />
          <div style={{ flex: 1 }}>
            <div style={{ fontWeight: 500 }}>{item.book?.title}</div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              作者: {item.book?.author || '未知'}
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              数量: {item.quantity} | 单价: ¥{item.price}
            </div>
          </div>
          <div style={{ textAlign: 'right' }}>
            <Text strong>¥{item.subtotal}</Text>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div>
      <Title level={2}>订单管理</Title>

      <Card>
        {/* 搜索和筛选 */}
        <Space style={{ marginBottom: 16 }} wrap>
          <Search
            placeholder="搜索订单号、用户手机号"
            allowClear
            style={{ width: 300 }}
            onSearch={(value) => console.log('搜索:', value)}
          />
          <Select
            placeholder="订单状态"
            allowClear
            style={{ width: 120 }}
            onChange={(value) => console.log('状态筛选:', value)}
          >
            <Option value="pending">待支付</Option>
            <Option value="paid">已支付</Option>
            <Option value="delivering">配送中</Option>
            <Option value="delivered">已送达</Option>
            <Option value="cancelled">已取消</Option>
          </Select>
          <Select
            placeholder="支付状态"
            allowClear
            style={{ width: 120 }}
            onChange={(value) => console.log('支付状态筛选:', value)}
          >
            <Option value="pending">待支付</Option>
            <Option value="paid">已支付</Option>
            <Option value="failed">支付失败</Option>
            <Option value="refunded">已退款</Option>
          </Select>
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={(dates) => console.log('日期筛选:', dates)}
          />
          <Button icon={<ExportOutlined />}>
            导出数据
          </Button>
        </Space>

        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination({ ...pagination, current: page, pageSize: pageSize || 20 });
              loadOrders();
            }
          }}
        />
      </Card>

      {/* 订单详情模态框 */}
      <Modal
        title="订单详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div>
            <Descriptions column={2} bordered style={{ marginBottom: 16 }}>
              <Descriptions.Item label="订单号">{selectedOrder.order_number}</Descriptions.Item>
              <Descriptions.Item label="订单状态">{getStatusTag(selectedOrder.status)}</Descriptions.Item>
              <Descriptions.Item label="下单时间">
                {dayjs(selectedOrder.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="支付状态">{getPaymentStatusTag(selectedOrder.payment_status)}</Descriptions.Item>
              <Descriptions.Item label="用户信息">
                {selectedOrder.user?.username || selectedOrder.user?.phone}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">
                {selectedOrder.delivery_phone}
              </Descriptions.Item>
              <Descriptions.Item label="配送地址" span={2}>
                {selectedOrder.delivery_address}
              </Descriptions.Item>
              <Descriptions.Item label="订单金额" span={2}>
                <Text strong style={{ color: '#f5222d', fontSize: '16px' }}>
                  ¥{selectedOrder.total_amount}
                </Text>
              </Descriptions.Item>
            </Descriptions>

            <Title level={5}>商品清单</Title>
            {selectedOrder.items && renderOrderItems(selectedOrder.items)}
          </div>
        )}
      </Modal>

      {/* 编辑订单状态模态框 */}
      <Modal
        title="更新订单状态"
        open={editVisible}
        onCancel={() => setEditVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleUpdateStatus}
        >
          <Form.Item
            name="status"
            label="订单状态"
            rules={[{ required: true, message: '请选择订单状态' }]}
          >
            <Select placeholder="请选择订单状态">
              <Option value="pending">待支付</Option>
              <Option value="paid">已支付</Option>
              <Option value="delivering">配送中</Option>
              <Option value="delivered">已送达</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="delivery_person"
            label="配送员"
          >
            <Input placeholder="请输入配送员姓名" />
          </Form.Item>

          <Form.Item
            name="delivery_person_phone"
            label="配送员电话"
          >
            <Input placeholder="请输入配送员电话" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminOrders;
