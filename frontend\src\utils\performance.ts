// 性能监控工具

// 性能指标类型
interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

// 性能监控类
class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  // 初始化性能观察器
  private initializeObservers(): void {
    // 观察 FCP 和 LCP
    if ('PerformanceObserver' in window) {
      try {
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.fcp = entry.startTime;
            }
          }
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.push(paintObserver);

        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.metrics.lcp = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);

        // 观察 FID
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.metrics.fid = (entry as any).processingStart - entry.startTime;
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);

        // 观察 CLS
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value;
            }
          }
          this.metrics.cls = clsValue;
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (error) {
        console.warn('Performance monitoring not supported:', error);
      }
    }
  }

  // 获取 TTFB
  public getTTFB(): number | undefined {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
      if (navigationEntries.length > 0) {
        const entry = navigationEntries[0];
        this.metrics.ttfb = entry.responseStart - entry.requestStart;
        return this.metrics.ttfb;
      }
    }
    return undefined;
  }

  // 获取所有指标
  public getMetrics(): PerformanceMetrics {
    this.getTTFB();
    return { ...this.metrics };
  }

  // 记录自定义指标
  public mark(name: string): void {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(name);
    }
  }

  // 测量两个标记之间的时间
  public measure(name: string, startMark: string, endMark?: string): number | undefined {
    if ('performance' in window && 'measure' in performance) {
      try {
        if (endMark) {
          performance.measure(name, startMark, endMark);
        } else {
          performance.measure(name, startMark);
        }
        
        const measures = performance.getEntriesByName(name, 'measure');
        if (measures.length > 0) {
          return measures[measures.length - 1].duration;
        }
      } catch (error) {
        console.warn('Performance measure failed:', error);
      }
    }
    return undefined;
  }

  // 清理观察器
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }

  // 上报性能数据
  public report(): void {
    const metrics = this.getMetrics();
    
    // 在开发环境下打印到控制台
    if (process.env.NODE_ENV === 'development') {
      console.group('Performance Metrics');
      console.log('First Contentful Paint (FCP):', metrics.fcp ? `${metrics.fcp.toFixed(2)}ms` : 'N/A');
      console.log('Largest Contentful Paint (LCP):', metrics.lcp ? `${metrics.lcp.toFixed(2)}ms` : 'N/A');
      console.log('First Input Delay (FID):', metrics.fid ? `${metrics.fid.toFixed(2)}ms` : 'N/A');
      console.log('Cumulative Layout Shift (CLS):', metrics.cls ? metrics.cls.toFixed(4) : 'N/A');
      console.log('Time to First Byte (TTFB):', metrics.ttfb ? `${metrics.ttfb.toFixed(2)}ms` : 'N/A');
      console.groupEnd();
    }

    // 在生产环境下可以发送到分析服务
    if (process.env.NODE_ENV === 'production') {
      // 这里可以发送到 Google Analytics、Sentry 等服务
      // analytics.track('performance_metrics', metrics);
    }
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// 页面加载完成后自动上报
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    // 延迟一段时间确保所有指标都被收集
    setTimeout(() => {
      performanceMonitor.report();
    }, 1000);
  });
}

// 页面卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    performanceMonitor.cleanup();
  });
}

// 工具函数：测量函数执行时间
export function measureFunction<T extends (...args: any[]) => any>(
  fn: T,
  name?: string
): T {
  return ((...args: Parameters<T>) => {
    const functionName = name || fn.name || 'anonymous';
    const startMark = `${functionName}-start`;
    const endMark = `${functionName}-end`;
    
    performanceMonitor.mark(startMark);
    const result = fn(...args);
    
    if (result instanceof Promise) {
      return result.finally(() => {
        performanceMonitor.mark(endMark);
        const duration = performanceMonitor.measure(functionName, startMark, endMark);
        if (process.env.NODE_ENV === 'development' && duration) {
          console.log(`Function ${functionName} took ${duration.toFixed(2)}ms`);
        }
      });
    } else {
      performanceMonitor.mark(endMark);
      const duration = performanceMonitor.measure(functionName, startMark, endMark);
      if (process.env.NODE_ENV === 'development' && duration) {
        console.log(`Function ${functionName} took ${duration.toFixed(2)}ms`);
      }
      return result;
    }
  }) as T;
}

// React Hook：使用性能监控
export function usePerformanceMonitor() {
  const mark = (name: string) => performanceMonitor.mark(name);
  const measure = (name: string, startMark: string, endMark?: string) => 
    performanceMonitor.measure(name, startMark, endMark);
  const getMetrics = () => performanceMonitor.getMetrics();
  
  return { mark, measure, getMetrics };
}

// 内存使用监控
export function getMemoryUsage(): any {
  if ('memory' in performance) {
    return {
      usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
      totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
      jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
    };
  }
  return null;
}

// 网络信息监控
export function getNetworkInfo(): any {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    return {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData
    };
  }
  return null;
}
