import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Divider,
  Image,
  Radio,
  message,
  Row,
  Col,
  Steps
} from 'antd';
import {
  EnvironmentOutlined,
  PhoneOutlined,
  UserOutlined,
  CreditCardOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { CartItem, OrderForm } from '../../types';
import { ordersService } from '../../services/orders';
import { useCartStore } from '../../stores/cartStore';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Step } = Steps;

const CreateContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
`;

const OrderSummary = styled(Card)`
  .item-row {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-image {
      width: 60px;
      height: 80px;
      margin-right: 16px;
      border-radius: 4px;
    }
    
    .item-info {
      flex: 1;
      
      .item-title {
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .item-details {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
    
    .item-price {
      text-align: right;
      
      .price {
        color: #f5222d;
        font-weight: 500;
      }
      
      .quantity {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
  
  .summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    
    &.total {
      font-size: 18px;
      font-weight: bold;
      color: #f5222d;
      border-top: 1px solid #f0f0f0;
      padding-top: 12px;
      margin-top: 16px;
    }
  }
`;

const OrderCreate: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { clearCart } = useCartStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);

  useEffect(() => {
    // 从路由状态获取购物车商品
    const items = location.state?.cartItems as CartItem[];
    if (!items || items.length === 0) {
      message.warning('没有选择商品');
      navigate('/cart');
      return;
    }
    setCartItems(items);
  }, [location.state, navigate]);

  const calculateTotal = () => {
    return cartItems.reduce((total, item) => total + (item.book.price * item.quantity), 0);
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      
      const orderData: OrderForm = {
        items: cartItems.map(item => ({
          book_id: item.book_id,
          quantity: item.quantity
        })),
        delivery_address: values.delivery_address,
        delivery_phone: values.delivery_phone
      };

      const response = await ordersService.createOrder(orderData);
      if (response.success) {
        message.success('订单创建成功');
        
        // 清空购物车中的相关商品
        cartItems.forEach(item => {
          // 这里可以从购物车中移除已下单的商品
        });
        
        // 跳转到订单详情页
        navigate(`/orders/${response.data?.id}`);
      }
    } catch (error) {
      console.error('创建订单失败:', error);
      message.error('创建订单失败');
    } finally {
      setLoading(false);
    }
  };

  const renderCartItem = (item: CartItem) => (
    <div key={item.book_id} className="item-row">
      <Image
        className="item-image"
        src={item.book.cover_image || '/images/book-placeholder.png'}
        alt={item.book.title}
        fallback="/images/book-placeholder.png"
        preview={false}
      />
      <div className="item-info">
        <div className="item-title">{item.book.title}</div>
        <div className="item-details">
          作者: {item.book.author || '未知'} | 状况: {item.book.condition}
        </div>
      </div>
      <div className="item-price">
        <div className="price">¥{item.book.price}</div>
        <div className="quantity">x{item.quantity}</div>
      </div>
    </div>
  );

  if (cartItems.length === 0) {
    return null;
  }

  return (
    <CreateContainer>
      <Card>
        <Title level={2}>创建订单</Title>
        
        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          <Step title="确认商品" icon={<CheckCircleOutlined />} />
          <Step title="填写信息" icon={<EnvironmentOutlined />} />
          <Step title="选择支付" icon={<CreditCardOutlined />} />
        </Steps>

        <Row gutter={[24, 24]}>
          <Col xs={24} lg={14}>
            <Card title="配送信息" style={{ marginBottom: 24 }}>
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  delivery_method: 'platform'
                }}
              >
                <Form.Item
                  name="delivery_address"
                  label="配送地址"
                  rules={[
                    { required: true, message: '请输入配送地址' },
                    { max: 500, message: '地址长度不能超过500字符' }
                  ]}
                >
                  <TextArea
                    rows={3}
                    placeholder="请输入详细的配送地址，包括省市区、街道、门牌号等"
                    prefix={<EnvironmentOutlined />}
                  />
                </Form.Item>

                <Form.Item
                  name="delivery_phone"
                  label="联系电话"
                  rules={[
                    { required: true, message: '请输入联系电话' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                  ]}
                >
                  <Input
                    placeholder="请输入联系电话"
                    prefix={<PhoneOutlined />}
                    maxLength={11}
                  />
                </Form.Item>

                <Form.Item
                  name="delivery_method"
                  label="配送方式"
                >
                  <Radio.Group>
                    <Radio value="platform">平台配送（免费）</Radio>
                    <Radio value="pickup" disabled>自提（暂不支持）</Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  name="notes"
                  label="备注信息"
                >
                  <TextArea
                    rows={2}
                    placeholder="如有特殊要求请在此说明（可选）"
                    maxLength={200}
                  />
                </Form.Item>
              </Form>
            </Card>

            <Card title="支付方式">
              <Radio.Group defaultValue="alipay" style={{ width: '100%' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Radio value="alipay">
                    <Space>
                      <img src="/images/alipay.png" alt="支付宝" style={{ width: 24, height: 24 }} />
                      支付宝
                    </Space>
                  </Radio>
                  <Radio value="wechat">
                    <Space>
                      <img src="/images/wechat-pay.png" alt="微信支付" style={{ width: 24, height: 24 }} />
                      微信支付
                    </Space>
                  </Radio>
                  <Radio value="offline" disabled>
                    <Space>
                      <CreditCardOutlined />
                      线下支付（暂不支持）
                    </Space>
                  </Radio>
                </Space>
              </Radio.Group>
            </Card>
          </Col>

          <Col xs={24} lg={10}>
            <OrderSummary title="订单摘要">
              <div style={{ marginBottom: 16 }}>
                {cartItems.map(renderCartItem)}
              </div>
              
              <Divider />
              
              <div className="summary-row">
                <span>商品总数:</span>
                <span>{cartItems.reduce((sum, item) => sum + item.quantity, 0)} 件</span>
              </div>
              <div className="summary-row">
                <span>商品总价:</span>
                <span>¥{calculateTotal().toFixed(2)}</span>
              </div>
              <div className="summary-row">
                <span>运费:</span>
                <span>免费</span>
              </div>
              <div className="summary-row total">
                <span>应付金额:</span>
                <span>¥{calculateTotal().toFixed(2)}</span>
              </div>
              
              <Divider />
              
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button
                  type="primary"
                  size="large"
                  loading={loading}
                  onClick={() => form.submit()}
                  block
                >
                  提交订单
                </Button>
                <Button
                  size="large"
                  onClick={() => navigate('/cart')}
                  block
                >
                  返回购物车
                </Button>
              </Space>
            </OrderSummary>
          </Col>
        </Row>
      </Card>
    </CreateContainer>
  );
};

export default OrderCreate;
