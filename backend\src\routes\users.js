const express = require('express');
const { User } = require('../models');
const { requireOwnerOrAdmin } = require('../middleware/auth');

const router = express.Router();

// 获取当前用户信息
router.get('/profile', async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 更新用户信息
router.put('/profile', async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const {
      username,
      email,
      avatar,
      contact_wechat,
      contact_qq,
      contact_phone_public
    } = req.body;

    // 检查用户名是否已被使用
    if (username && username !== user.username) {
      const existingUser = await User.findOne({ 
        where: { 
          username,
          id: { [require('sequelize').Op.ne]: user.id }
        }
      });
      
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '用户名已被使用'
        });
      }
    }

    // 检查邮箱是否已被使用
    if (email && email !== user.email) {
      const existingEmail = await User.findOne({ 
        where: { 
          email,
          id: { [require('sequelize').Op.ne]: user.id }
        }
      });
      
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被使用'
        });
      }
    }

    await user.update({
      username: username || user.username,
      email: email !== undefined ? email : user.email,
      avatar: avatar !== undefined ? avatar : user.avatar,
      contact_wechat: contact_wechat !== undefined ? contact_wechat : user.contact_wechat,
      contact_qq: contact_qq !== undefined ? contact_qq : user.contact_qq,
      contact_phone_public: contact_phone_public !== undefined ? contact_phone_public : user.contact_phone_public
    });

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: user
    });
  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '更新用户信息失败'
    });
  }
});

// 修改密码
router.put('/password', async (req, res) => {
  try {
    const { current_password, new_password } = req.body;

    if (!current_password || !new_password) {
      return res.status(400).json({
        success: false,
        message: '当前密码和新密码都是必填项'
      });
    }

    if (new_password.length < 6 || new_password.length > 20) {
      return res.status(400).json({
        success: false,
        message: '新密码长度必须在6-20位之间'
      });
    }

    const user = await User.findByPk(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证当前密码
    const isValidPassword = await user.validatePassword(current_password);
    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        message: '当前密码错误'
      });
    }

    // 更新密码
    await user.update({ password_hash: new_password });

    res.json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({
      success: false,
      message: '修改密码失败'
    });
  }
});

// 获取用户统计信息
router.get('/stats', async (req, res) => {
  try {
    const { Order, Book } = require('../models');
    
    // 获取订单统计
    const orderStats = await Order.findAll({
      where: { user_id: req.user.id },
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // 获取购买图书数量
    const totalBooks = await Order.count({
      where: { 
        user_id: req.user.id,
        status: { [require('sequelize').Op.in]: ['paid', 'delivering', 'delivered'] }
      },
      include: [
        {
          model: require('../models').OrderItem,
          as: 'items',
          attributes: []
        }
      ]
    });

    // 获取总消费金额
    const totalSpent = await Order.sum('total_amount', {
      where: { 
        user_id: req.user.id,
        payment_status: 'paid'
      }
    });

    res.json({
      success: true,
      data: {
        order_stats: orderStats,
        total_books: totalBooks || 0,
        total_spent: totalSpent || 0
      }
    });
  } catch (error) {
    console.error('获取用户统计信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计信息失败'
    });
  }
});

// 获取用户详情（管理员功能）
router.get('/:id', requireOwnerOrAdmin('id'), async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('获取用户详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户详情失败'
    });
  }
});

module.exports = router;
