import React from 'react';
import { Breadcrumb, Typography } from 'antd';
import { HomeOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import EnhancedCart from '../../components/business/EnhancedCart';

const { Title } = Typography;

const CartPageContainer = styled.div`
  min-height: 100vh;
  background: #f5f5f5;
  
  .page-header {
    background: white;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 24px;
      
      .breadcrumb {
        margin-bottom: 8px;
      }
      
      .page-title {
        margin: 0;
        font-size: 24px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 12px;
        
        .cart-icon {
          color: #1677ff;
        }
      }
    }
  }
`;

interface CartPageProps {}

const CartPage: React.FC<CartPageProps> = () => {
  const navigate = useNavigate();

  return (
    <CartPageContainer>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <HomeOutlined />
              <span onClick={() => navigate('/')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                首页
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <ShoppingCartOutlined />
              购物车
            </Breadcrumb.Item>
          </Breadcrumb>
          
          <Title level={2} className="page-title">
            <ShoppingCartOutlined className="cart-icon" />
            我的购物车
          </Title>
        </div>
      </div>

      {/* 购物车内容 */}
      <EnhancedCart />
    </CartPageContainer>
  );
};

export default CartPage;
