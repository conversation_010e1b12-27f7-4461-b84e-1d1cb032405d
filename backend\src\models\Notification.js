const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define('Notification', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  type: {
    type: DataTypes.ENUM(
      'system',      // 系统通知
      'order',       // 订单相关
      'book',        // 图书相关
      'review',      // 评论相关
      'promotion',   // 促销活动
      'security',    // 安全提醒
      'announcement' // 公告
    ),
    defaultValue: 'system'
  },
  priority: {
    type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
    defaultValue: 'normal'
  },
  status: {
    type: DataTypes.ENUM('unread', 'read', 'archived'),
    defaultValue: 'unread'
  },
  action_type: {
    type: DataTypes.STRING(50),
    allowNull: true // 如 'view_order', 'view_book', 'view_review'
  },
  action_data: {
    type: DataTypes.JSONB,
    allowNull: true // 存储相关的数据，如订单ID、图书ID等
  },
  sender_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  scheduled_at: {
    type: DataTypes.DATE,
    allowNull: true // 定时发送时间
  },
  sent_at: {
    type: DataTypes.DATE,
    allowNull: true // 实际发送时间
  },
  read_at: {
    type: DataTypes.DATE,
    allowNull: true // 阅读时间
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: true // 过期时间
  },
  is_global: {
    type: DataTypes.BOOLEAN,
    defaultValue: false // 是否为全局通知
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true // 额外的元数据
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'notifications',
  timestamps: false,
  indexes: [
    {
      fields: ['user_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['scheduled_at']
    },
    {
      fields: ['is_global']
    },
    {
      fields: ['user_id', 'status']
    },
    {
      fields: ['user_id', 'type']
    }
  ]
});

module.exports = Notification;
