import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Typography,
  Image,
  Popconfirm,
  message,
  Modal,
  Form,
  InputNumber,
  Upload,
  Row,
  Col
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  UploadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import styled from 'styled-components';
import { Book, Category, BookForm } from '../../types';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';
import { uploadService } from '../../services/upload';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;
const { TextArea } = Input;

const BookImage = styled(Image)`
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
`;

const AdminBooks: React.FC = () => {
  const [books, setBooks] = useState<Book[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingBook, setEditingBook] = useState<Book | null>(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  useEffect(() => {
    loadBooks();
    loadCategories();
  }, []);

  const loadBooks = async () => {
    try {
      setLoading(true);
      const response = await booksService.getBooks({
        page: pagination.current,
        limit: pagination.pageSize
      });
      if (response.success) {
        setBooks(response.data.books || []);
        setPagination({
          ...pagination,
          total: response.data.pagination.total_items
        });
      }
    } catch (error) {
      console.error('加载图书列表失败:', error);
      message.error('加载图书列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await categoriesService.getFlatCategories();
      if (response.success) {
        setCategories(response.data || []);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const handleAdd = () => {
    setEditingBook(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (book: Book) => {
    setEditingBook(book);
    form.setFieldsValue({
      ...book,
      images: book.images?.map((url, index) => ({
        uid: index.toString(),
        name: `image-${index}`,
        status: 'done',
        url
      })) || []
    });
    setModalVisible(true);
  };

  const handleDelete = async (bookId: string) => {
    try {
      const response = await booksService.deleteBook(bookId);
      if (response.success) {
        message.success('图书删除成功');
        loadBooks();
      }
    } catch (error) {
      console.error('删除图书失败:', error);
      message.error('删除图书失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const bookData: BookForm = {
        ...values,
        images: values.images?.map((file: any) => file.url || file.response?.url).filter(Boolean) || []
      };

      let response;
      if (editingBook) {
        response = await booksService.updateBook(editingBook.id, bookData);
      } else {
        response = await booksService.createBook(bookData);
      }

      if (response.success) {
        message.success(editingBook ? '图书更新成功' : '图书创建成功');
        setModalVisible(false);
        loadBooks();
      }
    } catch (error) {
      console.error('保存图书失败:', error);
      message.error('保存图书失败');
    }
  };

  const handleImageUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      const response = await uploadService.uploadBookImages([file]);
      if (response.success && response.data?.files?.[0]) {
        onSuccess(response.data.files[0]);
      } else {
        throw new Error('上传失败');
      }
    } catch (error) {
      onError(error);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      '上架': { color: 'green', text: '上架' },
      '下架': { color: 'orange', text: '下架' },
      '缺货': { color: 'red', text: '缺货' },
      '预售': { color: 'blue', text: '预售' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getConditionTag = (condition: string) => {
    const conditionMap: Record<string, { color: string }> = {
      '全新': { color: 'green' },
      '九成新': { color: 'blue' },
      '八成新': { color: 'orange' },
      '七成新': { color: 'gold' },
      '六成新': { color: 'red' }
    };
    const config = conditionMap[condition] || { color: 'default' };
    return <Tag color={config.color}>{condition}</Tag>;
  };

  const columns: ColumnsType<Book> = [
    {
      title: '图书信息',
      key: 'book',
      width: 300,
      render: (_, record) => (
        <Space>
          <BookImage
            src={record.cover_image || '/images/book-placeholder.png'}
            alt={record.title}
            fallback="/images/book-placeholder.png"
            preview={false}
          />
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>{record.title}</div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              作者: {record.author || '未知'}
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              ISBN: {record.isbn || '暂无'}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category) => category?.name || '-'
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      render: (price) => `¥${price}`
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      width: 80
    },
    {
      title: '状况',
      dataIndex: 'condition',
      key: 'condition',
      width: 100,
      render: (condition) => getConditionTag(condition)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status)
    },
    {
      title: '浏览/销量',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px' }}>浏览: {record.views}</div>
          <div style={{ fontSize: '12px' }}>销量: {record.sales_count}</div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => window.open(`/books/${record.id}`, '_blank')}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这本图书吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Title level={2}>图书管理</Title>

      <Card>
        {/* 搜索和操作栏 */}
        <Space style={{ marginBottom: 16 }} wrap>
          <Search
            placeholder="搜索图书标题、作者、ISBN"
            allowClear
            style={{ width: 300 }}
            onSearch={(value) => console.log('搜索:', value)}
          />
          <Select
            placeholder="选择分类"
            allowClear
            style={{ width: 150 }}
            onChange={(value) => console.log('分类筛选:', value)}
          >
            {categories.map(category => (
              <Option key={category.id} value={category.id}>
                {category.name}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="图书状态"
            allowClear
            style={{ width: 120 }}
            onChange={(value) => console.log('状态筛选:', value)}
          >
            <Option value="上架">上架</Option>
            <Option value="下架">下架</Option>
            <Option value="缺货">缺货</Option>
            <Option value="预售">预售</Option>
          </Select>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加图书
          </Button>
          <Button icon={<UploadOutlined />}>
            批量导入
          </Button>
        </Space>

        <Table
          columns={columns}
          dataSource={books}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination({ ...pagination, current: page, pageSize: pageSize || 20 });
              loadBooks();
            }
          }}
        />
      </Card>

      {/* 添加/编辑图书模态框 */}
      <Modal
        title={editingBook ? '编辑图书' : '添加图书'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="title"
                label="图书标题"
                rules={[{ required: true, message: '请输入图书标题' }]}
              >
                <Input placeholder="请输入图书标题" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="author"
                label="作者"
              >
                <Input placeholder="请输入作者" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="publisher"
                label="出版社"
              >
                <Input placeholder="请输入出版社" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="isbn"
                label="ISBN"
              >
                <Input placeholder="请输入ISBN" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="category_id"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="condition"
                label="图书状况"
                rules={[{ required: true, message: '请选择图书状况' }]}
              >
                <Select placeholder="请选择图书状况">
                  <Option value="全新">全新</Option>
                  <Option value="九成新">九成新</Option>
                  <Option value="八成新">八成新</Option>
                  <Option value="七成新">七成新</Option>
                  <Option value="六成新">六成新</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="price"
                label="售价"
                rules={[{ required: true, message: '请输入售价' }]}
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入售价"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="original_price"
                label="原价"
              >
                <InputNumber
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  placeholder="请输入原价"
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                name="stock"
                label="库存"
                rules={[{ required: true, message: '请输入库存' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="请输入库存"
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="description"
                label="图书简介"
              >
                <TextArea
                  rows={4}
                  placeholder="请输入图书简介"
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="images"
                label="图书图片"
              >
                <Upload
                  listType="picture-card"
                  customRequest={handleImageUpload}
                  beforeUpload={(file) => {
                    const isImage = file.type.startsWith('image/');
                    if (!isImage) {
                      message.error('只能上传图片文件');
                      return false;
                    }
                    const isLt5M = file.size / 1024 / 1024 < 5;
                    if (!isLt5M) {
                      message.error('图片大小不能超过5MB');
                      return false;
                    }
                    return true;
                  }}
                  maxCount={5}
                >
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传图片</div>
                  </div>
                </Upload>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminBooks;
