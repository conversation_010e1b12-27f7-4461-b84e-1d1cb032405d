const express = require('express');
const notificationService = require('../services/notificationService');
const { authenticateToken, requireRole } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取用户通知列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      status = 'all',
      priority
    } = req.query;

    const result = await notificationService.getUserNotifications(req.user.id, {
      page: parseInt(page),
      limit: parseInt(limit),
      type,
      status,
      priority
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    logger.error('获取通知列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知列表失败'
    });
  }
});

// 获取未读通知数量
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const count = await notificationService.getUnreadCount(req.user.id);

    res.json({
      success: true,
      data: { count }
    });
  } catch (error) {
    logger.error('获取未读通知数量失败:', error);
    res.status(500).json({
      success: false,
      message: '获取未读通知数量失败'
    });
  }
});

// 获取通知统计
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await notificationService.getNotificationStats(req.user.id);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('获取通知统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知统计失败'
    });
  }
});

// 标记通知为已读
router.put('/:id/read', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    await notificationService.markAsRead(id, req.user.id);

    res.json({
      success: true,
      message: '标记已读成功'
    });
  } catch (error) {
    logger.error('标记通知已读失败:', error);
    res.status(500).json({
      success: false,
      message: '标记已读失败'
    });
  }
});

// 批量标记通知为已读
router.put('/batch/read', authenticateToken, async (req, res) => {
  try {
    const { notification_ids } = req.body;

    if (!notification_ids || !Array.isArray(notification_ids)) {
      return res.status(400).json({
        success: false,
        message: '通知ID列表不能为空'
      });
    }

    await notificationService.markAsRead(notification_ids, req.user.id);

    res.json({
      success: true,
      message: '批量标记已读成功'
    });
  } catch (error) {
    logger.error('批量标记通知已读失败:', error);
    res.status(500).json({
      success: false,
      message: '批量标记已读失败'
    });
  }
});

// 标记所有通知为已读
router.put('/all/read', authenticateToken, async (req, res) => {
  try {
    await notificationService.markAllAsRead(req.user.id);

    res.json({
      success: true,
      message: '全部标记已读成功'
    });
  } catch (error) {
    logger.error('标记所有通知已读失败:', error);
    res.status(500).json({
      success: false,
      message: '全部标记已读失败'
    });
  }
});

// 删除通知
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await notificationService.deleteNotification(id, req.user.id);

    if (result === 0) {
      return res.status(404).json({
        success: false,
        message: '通知不存在或无权删除'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    logger.error('删除通知失败:', error);
    res.status(500).json({
      success: false,
      message: '删除失败'
    });
  }
});

// 管理员：发送系统通知
router.post('/system', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const {
      user_ids,
      title,
      content,
      priority = 'normal',
      action_type,
      action_data,
      metadata
    } = req.body;

    if (!user_ids || !Array.isArray(user_ids) || user_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '用户ID列表不能为空'
      });
    }

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '标题和内容不能为空'
      });
    }

    const notifications = await notificationService.sendSystemNotification(
      user_ids,
      title,
      content,
      {
        priority,
        action_type,
        action_data,
        metadata
      }
    );

    logger.info(`管理员 ${req.user.id} 发送系统通知给 ${user_ids.length} 个用户`);

    res.json({
      success: true,
      data: notifications,
      message: '系统通知发送成功'
    });
  } catch (error) {
    logger.error('发送系统通知失败:', error);
    res.status(500).json({
      success: false,
      message: '发送系统通知失败'
    });
  }
});

// 管理员：发送全局公告
router.post('/announcement', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const {
      title,
      content,
      priority = 'normal',
      expires_at,
      metadata
    } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '标题和内容不能为空'
      });
    }

    const notifications = await notificationService.sendGlobalAnnouncement(
      title,
      content,
      {
        priority,
        expires_at: expires_at ? new Date(expires_at) : null,
        metadata
      }
    );

    logger.info(`管理员 ${req.user.id} 发送全局公告`);

    res.json({
      success: true,
      data: notifications,
      message: '全局公告发送成功'
    });
  } catch (error) {
    logger.error('发送全局公告失败:', error);
    res.status(500).json({
      success: false,
      message: '发送全局公告失败'
    });
  }
});

// 管理员：发送促销通知
router.post('/promotion', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const {
      user_ids,
      title,
      content,
      promotion_data
    } = req.body;

    if (!user_ids || !Array.isArray(user_ids) || user_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '用户ID列表不能为空'
      });
    }

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '标题和内容不能为空'
      });
    }

    const notifications = await notificationService.sendPromotionNotification(
      user_ids,
      title,
      content,
      promotion_data
    );

    logger.info(`管理员 ${req.user.id} 发送促销通知给 ${user_ids.length} 个用户`);

    res.json({
      success: true,
      data: notifications,
      message: '促销通知发送成功'
    });
  } catch (error) {
    logger.error('发送促销通知失败:', error);
    res.status(500).json({
      success: false,
      message: '发送促销通知失败'
    });
  }
});

// 管理员：清理过期通知
router.delete('/cleanup/expired', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const result = await notificationService.cleanupExpiredNotifications();

    logger.info(`管理员 ${req.user.id} 清理过期通知: ${result} 条`);

    res.json({
      success: true,
      data: { deleted_count: result },
      message: '过期通知清理完成'
    });
  } catch (error) {
    logger.error('清理过期通知失败:', error);
    res.status(500).json({
      success: false,
      message: '清理过期通知失败'
    });
  }
});

module.exports = router;
