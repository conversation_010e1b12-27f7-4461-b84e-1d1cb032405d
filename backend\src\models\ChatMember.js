const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ChatMember = sequelize.define('ChatMember', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  room_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'chat_rooms',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  role: {
    type: DataTypes.ENUM('member', 'admin', 'owner'),
    defaultValue: 'member'
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  is_muted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  muted_until: {
    type: DataTypes.DATE,
    allowNull: true
  },
  last_read_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  last_message_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'chat_messages',
      key: 'id'
    }
  },
  notification_settings: {
    type: DataTypes.JSONB,
    defaultValue: {
      mentions: true,
      all_messages: true,
      sound: true
    }
  },
  joined_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  left_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'chat_members',
  timestamps: false,
  indexes: [
    {
      unique: true,
      fields: ['room_id', 'user_id']
    },
    {
      fields: ['room_id']
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['role']
    },
    {
      fields: ['last_read_at']
    },
    {
      fields: ['joined_at']
    }
  ]
});

module.exports = ChatMember;
