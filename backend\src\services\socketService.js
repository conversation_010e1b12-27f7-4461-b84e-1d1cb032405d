const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { User, ChatRoom, ChatMessage, ChatMember } = require('../models');
const logger = require('../utils/logger');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
    this.userSockets = new Map(); // socketId -> userId
    this.roomMembers = new Map(); // roomId -> Set of userIds
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
        credentials: true
      }
    });

    this.io.use(this.authenticateSocket.bind(this));
    this.io.on('connection', this.handleConnection.bind(this));

    logger.info('Socket.IO server initialized');
  }

  async authenticateSocket(socket, next) {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findByPk(decoded.id);
      
      if (!user) {
        return next(new Error('User not found'));
      }

      socket.userId = user.id;
      socket.user = user;
      next();
    } catch (error) {
      logger.error('Socket authentication error:', error);
      next(new Error('Authentication error'));
    }
  }

  handleConnection(socket) {
    const userId = socket.userId;
    logger.info(`User ${userId} connected with socket ${socket.id}`);

    // 记录用户连接
    this.connectedUsers.set(userId, socket.id);
    this.userSockets.set(socket.id, userId);

    // 加入用户的聊天室
    this.joinUserRooms(socket);

    // 监听事件
    socket.on('join_room', (data) => this.handleJoinRoom(socket, data));
    socket.on('leave_room', (data) => this.handleLeaveRoom(socket, data));
    socket.on('send_message', (data) => this.handleSendMessage(socket, data));
    socket.on('edit_message', (data) => this.handleEditMessage(socket, data));
    socket.on('delete_message', (data) => this.handleDeleteMessage(socket, data));
    socket.on('typing_start', (data) => this.handleTypingStart(socket, data));
    socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));
    socket.on('mark_read', (data) => this.handleMarkRead(socket, data));

    socket.on('disconnect', () => this.handleDisconnect(socket));
  }

  async joinUserRooms(socket) {
    try {
      const userId = socket.userId;
      
      // 获取用户参与的所有聊天室
      const memberships = await ChatMember.findAll({
        where: {
          user_id: userId,
          left_at: null
        },
        include: [{
          model: ChatRoom,
          as: 'room',
          where: { status: 'active' }
        }]
      });

      // 加入所有聊天室
      for (const membership of memberships) {
        const roomId = membership.room_id;
        socket.join(roomId);
        
        // 更新房间成员记录
        if (!this.roomMembers.has(roomId)) {
          this.roomMembers.set(roomId, new Set());
        }
        this.roomMembers.get(roomId).add(userId);
      }

      logger.info(`User ${userId} joined ${memberships.length} rooms`);
    } catch (error) {
      logger.error('Error joining user rooms:', error);
    }
  }

  async handleJoinRoom(socket, data) {
    try {
      const { room_id } = data;
      const userId = socket.userId;

      // 验证用户是否有权限加入房间
      const membership = await ChatMember.findOne({
        where: {
          room_id,
          user_id: userId,
          left_at: null
        }
      });

      if (!membership) {
        socket.emit('error', { message: '无权限加入此聊天室' });
        return;
      }

      socket.join(room_id);
      
      // 更新房间成员记录
      if (!this.roomMembers.has(room_id)) {
        this.roomMembers.set(room_id, new Set());
      }
      this.roomMembers.get(room_id).add(userId);

      // 通知房间其他成员
      socket.to(room_id).emit('user_joined', {
        user_id: userId,
        username: socket.user.username
      });

      socket.emit('joined_room', { room_id });
      logger.info(`User ${userId} joined room ${room_id}`);
    } catch (error) {
      logger.error('Error joining room:', error);
      socket.emit('error', { message: '加入聊天室失败' });
    }
  }

  handleLeaveRoom(socket, data) {
    const { room_id } = data;
    const userId = socket.userId;

    socket.leave(room_id);
    
    // 更新房间成员记录
    if (this.roomMembers.has(room_id)) {
      this.roomMembers.get(room_id).delete(userId);
    }

    // 通知房间其他成员
    socket.to(room_id).emit('user_left', {
      user_id: userId,
      username: socket.user.username
    });

    socket.emit('left_room', { room_id });
    logger.info(`User ${userId} left room ${room_id}`);
  }

  async handleSendMessage(socket, data) {
    try {
      const { room_id, content, type = 'text', reply_to, attachments } = data;
      const userId = socket.userId;

      // 验证用户是否在房间中
      const membership = await ChatMember.findOne({
        where: {
          room_id,
          user_id: userId,
          left_at: null
        }
      });

      if (!membership) {
        socket.emit('error', { message: '您不在此聊天室中' });
        return;
      }

      // 检查是否被禁言
      if (membership.is_muted && membership.muted_until && new Date() < membership.muted_until) {
        socket.emit('error', { message: '您已被禁言' });
        return;
      }

      // 创建消息
      const message = await ChatMessage.create({
        room_id,
        sender_id: userId,
        content,
        type,
        reply_to,
        attachments: attachments || []
      });

      // 获取完整的消息信息
      const fullMessage = await ChatMessage.findByPk(message.id, {
        include: [
          {
            model: User,
            as: 'sender',
            attributes: ['id', 'username', 'avatar']
          },
          {
            model: ChatMessage,
            as: 'replyToMessage',
            include: [{
              model: User,
              as: 'sender',
              attributes: ['id', 'username']
            }]
          }
        ]
      });

      // 更新房间最后消息时间
      await ChatRoom.update(
        { last_message_at: new Date() },
        { where: { id: room_id } }
      );

      // 发送消息给房间所有成员
      this.io.to(room_id).emit('new_message', fullMessage);

      logger.info(`Message sent in room ${room_id} by user ${userId}`);
    } catch (error) {
      logger.error('Error sending message:', error);
      socket.emit('error', { message: '发送消息失败' });
    }
  }

  async handleEditMessage(socket, data) {
    try {
      const { message_id, content } = data;
      const userId = socket.userId;

      const message = await ChatMessage.findOne({
        where: {
          id: message_id,
          sender_id: userId,
          is_deleted: false
        }
      });

      if (!message) {
        socket.emit('error', { message: '消息不存在或无权限编辑' });
        return;
      }

      await message.update({
        content,
        is_edited: true,
        edited_at: new Date()
      });

      // 通知房间成员消息已编辑
      this.io.to(message.room_id).emit('message_edited', {
        message_id,
        content,
        edited_at: message.edited_at
      });

      logger.info(`Message ${message_id} edited by user ${userId}`);
    } catch (error) {
      logger.error('Error editing message:', error);
      socket.emit('error', { message: '编辑消息失败' });
    }
  }

  async handleDeleteMessage(socket, data) {
    try {
      const { message_id } = data;
      const userId = socket.userId;

      const message = await ChatMessage.findOne({
        where: {
          id: message_id,
          sender_id: userId,
          is_deleted: false
        }
      });

      if (!message) {
        socket.emit('error', { message: '消息不存在或无权限删除' });
        return;
      }

      await message.update({
        is_deleted: true,
        deleted_at: new Date()
      });

      // 通知房间成员消息已删除
      this.io.to(message.room_id).emit('message_deleted', {
        message_id
      });

      logger.info(`Message ${message_id} deleted by user ${userId}`);
    } catch (error) {
      logger.error('Error deleting message:', error);
      socket.emit('error', { message: '删除消息失败' });
    }
  }

  handleTypingStart(socket, data) {
    const { room_id } = data;
    const userId = socket.userId;

    socket.to(room_id).emit('user_typing', {
      user_id: userId,
      username: socket.user.username,
      typing: true
    });
  }

  handleTypingStop(socket, data) {
    const { room_id } = data;
    const userId = socket.userId;

    socket.to(room_id).emit('user_typing', {
      user_id: userId,
      username: socket.user.username,
      typing: false
    });
  }

  async handleMarkRead(socket, data) {
    try {
      const { room_id, message_id } = data;
      const userId = socket.userId;

      await ChatMember.update(
        {
          last_read_at: new Date(),
          last_message_id: message_id
        },
        {
          where: {
            room_id,
            user_id: userId
          }
        }
      );

      // 通知房间其他成员
      socket.to(room_id).emit('message_read', {
        user_id: userId,
        message_id,
        read_at: new Date()
      });
    } catch (error) {
      logger.error('Error marking message as read:', error);
    }
  }

  handleDisconnect(socket) {
    const userId = socket.userId;
    
    // 清理连接记录
    this.connectedUsers.delete(userId);
    this.userSockets.delete(socket.id);

    // 从所有房间成员记录中移除
    for (const [roomId, members] of this.roomMembers.entries()) {
      members.delete(userId);
      if (members.size === 0) {
        this.roomMembers.delete(roomId);
      }
    }

    logger.info(`User ${userId} disconnected`);
  }

  // 发送通知给特定用户
  sendNotificationToUser(userId, notification) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      this.io.to(socketId).emit('notification', notification);
    }
  }

  // 发送通知给房间所有成员
  sendNotificationToRoom(roomId, notification) {
    this.io.to(roomId).emit('notification', notification);
  }

  // 获取在线用户数量
  getOnlineUsersCount() {
    return this.connectedUsers.size;
  }

  // 获取房间在线成员
  getRoomOnlineMembers(roomId) {
    const members = this.roomMembers.get(roomId);
    return members ? Array.from(members) : [];
  }
}

module.exports = new SocketService();
