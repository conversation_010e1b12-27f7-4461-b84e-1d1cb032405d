import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Select,
  DatePicker,
  Table,
  Progress,
  Tag,
  Avatar,
  Space,
  Typography,
  Spin,
  Button,
  message
} from 'antd';
import {
  UserOutlined,
  BookOutlined,
  ShoppingOutlined,
  DollarOutlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  DownloadOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
// import { Line, Column, Pie, Area } from '@ant-design/plots';
import { analyticsService, OverviewData, SalesTrendData, UserBehaviorData, BooksAnalysisData } from '../../services/analytics';
import LoadingState from '../ui/LoadingState';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const DashboardContainer = styled.div`
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
  
  .dashboard-header {
    margin-bottom: 24px;
    
    .header-title {
      margin-bottom: 8px;
    }
    
    .header-controls {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }
  }
  
  .stats-cards {
    margin-bottom: 24px;
    
    .stat-card {
      .ant-card-body {
        padding: 20px;
      }
      
      .stat-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        .stat-info {
          flex: 1;
          
          .stat-title {
            color: #8c8c8c;
            font-size: 14px;
            margin-bottom: 8px;
          }
          
          .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #262626;
            margin-bottom: 4px;
          }
          
          .stat-change {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            
            &.positive {
              color: #52c41a;
            }
            
            &.negative {
              color: #ff4d4f;
            }
          }
        }
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: white;
          
          &.users {
            background: linear-gradient(135deg, #667eea, #764ba2);
          }
          
          &.books {
            background: linear-gradient(135deg, #f093fb, #f5576c);
          }
          
          &.orders {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
          }
          
          &.revenue {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
          }
        }
      }
    }
  }
  
  .chart-card {
    margin-bottom: 24px;
    
    .chart-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      
      .chart-title {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
      }
      
      .chart-controls {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .activity-list {
    .activity-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .activity-avatar {
        margin-right: 12px;
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-size: 14px;
          color: #262626;
          margin-bottom: 4px;
        }
        
        .activity-time {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .activity-amount {
        font-weight: 600;
        color: #52c41a;
      }
    }
  }
`;

interface DashboardProps {}

const Dashboard: React.FC<DashboardProps> = () => {
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('30d');
  const [overviewData, setOverviewData] = useState<OverviewData | null>(null);
  const [salesTrend, setSalesTrend] = useState<SalesTrendData[]>([]);
  const [userBehavior, setUserBehavior] = useState<UserBehaviorData | null>(null);
  const [booksAnalysis, setBooksAnalysis] = useState<BooksAnalysisData | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, [period]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [overviewRes, salesRes, userRes, booksRes] = await Promise.all([
        analyticsService.getOverview(period),
        analyticsService.getSalesTrend(period),
        analyticsService.getUserBehavior(period),
        analyticsService.getBooksAnalysis()
      ]);

      if (overviewRes.success) setOverviewData(overviewRes.data);
      if (salesRes.success) setSalesTrend(salesRes.data.sales_trend);
      if (userRes.success) setUserBehavior(userRes.data);
      if (booksRes.success) setBooksAnalysis(booksRes.data);
    } catch (error) {
      message.error('加载数据失败');
      console.error('Dashboard data loading error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (type: string) => {
    try {
      const blob = await analyticsService.exportData(type as any, period);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type}_${period}_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? <TrendingUpOutlined /> : <TrendingDownOutlined />;
  };

  const getChangeClass = (change: number) => {
    return change >= 0 ? 'positive' : 'negative';
  };

  // 简化的图表数据处理
  const chartData = {
    salesTrend: salesTrend.map(item => ({
      date: item.date,
      revenue: item.revenue,
      orders: item.order_count
    })),
    categoryDistribution: booksAnalysis?.category_distribution || []
  };

  if (loading) {
    return <LoadingState loading={true} minHeight="100vh" />;
  }

  return (
    <DashboardContainer>
      <div className="dashboard-header">
        <Title level={2} className="header-title">数据仪表板</Title>
        <div className="header-controls">
          <Select value={period} onChange={setPeriod} style={{ width: 120 }}>
            <Option value="7d">最近7天</Option>
            <Option value="30d">最近30天</Option>
            <Option value="90d">最近90天</Option>
            <Option value="1y">最近1年</Option>
          </Select>
          <Button icon={<ReloadOutlined />} onClick={loadDashboardData}>
            刷新
          </Button>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={() => handleExport('overview')}
          >
            导出数据
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[24, 24]} className="stats-cards">
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <div className="stat-content">
              <div className="stat-info">
                <div className="stat-title">总用户数</div>
                <div className="stat-value">{overviewData?.overview.total_users || 0}</div>
                <div className={`stat-change ${getChangeClass(overviewData?.overview.new_users || 0)}`}>
                  {getChangeIcon(overviewData?.overview.new_users || 0)}
                  新增 {overviewData?.overview.new_users || 0}
                </div>
              </div>
              <div className="stat-icon users">
                <UserOutlined />
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <div className="stat-content">
              <div className="stat-info">
                <div className="stat-title">图书总数</div>
                <div className="stat-value">{overviewData?.overview.total_books || 0}</div>
                <div className={`stat-change ${getChangeClass(overviewData?.overview.new_books || 0)}`}>
                  {getChangeIcon(overviewData?.overview.new_books || 0)}
                  新增 {overviewData?.overview.new_books || 0}
                </div>
              </div>
              <div className="stat-icon books">
                <BookOutlined />
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <div className="stat-content">
              <div className="stat-info">
                <div className="stat-title">订单总数</div>
                <div className="stat-value">{overviewData?.overview.total_orders || 0}</div>
                <div className={`stat-change ${getChangeClass(overviewData?.overview.new_orders || 0)}`}>
                  {getChangeIcon(overviewData?.overview.new_orders || 0)}
                  新增 {overviewData?.overview.new_orders || 0}
                </div>
              </div>
              <div className="stat-icon orders">
                <ShoppingOutlined />
              </div>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <div className="stat-content">
              <div className="stat-info">
                <div className="stat-title">总收入</div>
                <div className="stat-value">¥{overviewData?.overview.total_revenue?.toFixed(2) || 0}</div>
                <div className={`stat-change ${getChangeClass(overviewData?.overview.period_revenue || 0)}`}>
                  {getChangeIcon(overviewData?.overview.period_revenue || 0)}
                  期间 ¥{overviewData?.overview.period_revenue?.toFixed(2) || 0}
                </div>
              </div>
              <div className="stat-icon revenue">
                <DollarOutlined />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* 销售趋势 */}
        <Col xs={24} lg={16}>
          <Card className="chart-card">
            <div className="chart-header">
              <Title level={4} className="chart-title">销售趋势</Title>
            </div>
            <Table
              dataSource={chartData.salesTrend.slice(0, 10)}
              pagination={false}
              size="small"
              columns={[
                {
                  title: '日期',
                  dataIndex: 'date',
                  key: 'date'
                },
                {
                  title: '订单数',
                  dataIndex: 'orders',
                  key: 'orders',
                  render: (value) => value || 0
                },
                {
                  title: '收入',
                  dataIndex: 'revenue',
                  key: 'revenue',
                  render: (value) => `¥${(value || 0).toFixed(2)}`
                }
              ]}
            />
          </Card>
        </Col>

        {/* 分类分布 */}
        <Col xs={24} lg={8}>
          <Card className="chart-card">
            <div className="chart-header">
              <Title level={4} className="chart-title">图书分类分布</Title>
            </div>
            <Table
              dataSource={chartData.categoryDistribution}
              pagination={false}
              size="small"
              columns={[
                {
                  title: '分类',
                  dataIndex: 'name',
                  key: 'name'
                },
                {
                  title: '图书数量',
                  dataIndex: 'book_count',
                  key: 'book_count',
                  render: (value) => (
                    <Progress
                      percent={Math.min((value / Math.max(...chartData.categoryDistribution.map(c => c.book_count))) * 100, 100)}
                      format={() => value}
                      size="small"
                    />
                  )
                }
              ]}
            />
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card className="chart-card">
            <div className="chart-header">
              <Title level={4} className="chart-title">最近活动</Title>
            </div>
            <div className="activity-list">
              {overviewData?.recent_activity.map(activity => (
                <div key={activity.id} className="activity-item">
                  <Avatar className="activity-avatar" icon={<UserOutlined />} />
                  <div className="activity-content">
                    <div className="activity-title">
                      {activity.user.username} 创建了订单 {activity.order_number}
                    </div>
                    <div className="activity-time">
                      {new Date(activity.created_at).toLocaleString()}
                    </div>
                  </div>
                  <div className="activity-amount">
                    ¥{activity.total_amount.toFixed(2)}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        {/* 热销图书 */}
        <Col xs={24} lg={12}>
          <Card className="chart-card">
            <div className="chart-header">
              <Title level={4} className="chart-title">热销图书</Title>
            </div>
            <Table
              dataSource={booksAnalysis?.top_selling_books || []}
              pagination={false}
              size="small"
              columns={[
                {
                  title: '图书',
                  dataIndex: 'title',
                  key: 'title',
                  render: (title, record) => (
                    <Space>
                      <Avatar src={record.cover_image} shape="square" />
                      <div>
                        <div>{title}</div>
                        <Text type="secondary">{record.author}</Text>
                      </div>
                    </Space>
                  )
                },
                {
                  title: '销量',
                  dataIndex: 'total_sold',
                  key: 'total_sold',
                  width: 80,
                  render: (value) => <Tag color="blue">{value}</Tag>
                }
              ]}
            />
          </Card>
        </Col>
      </Row>
    </DashboardContainer>
  );
};

export default Dashboard;
