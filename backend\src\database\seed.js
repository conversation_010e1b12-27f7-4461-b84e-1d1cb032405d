const { sequelize, User, Category, Book } = require('../models');
require('dotenv').config();

async function seedDatabase() {
  try {
    console.log('开始初始化数据库...');

    // 同步数据库模型
    await sequelize.sync({ force: true });
    console.log('数据库模型同步完成');

    // 创建超级管理员
    const superAdmin = await User.create({
      username: 'superadmin',
      phone: '13800000000',
      email: '<EMAIL>',
      password_hash: 'admin123456',
      role: 'super_admin',
      phone_verified: true,
      email_verified: true
    });
    console.log('超级管理员创建完成');

    // 创建普通管理员
    const admin = await User.create({
      username: 'admin',
      phone: '13800000001',
      email: '<EMAIL>',
      password_hash: 'admin123456',
      role: 'admin',
      phone_verified: true,
      email_verified: true,
      created_by: superAdmin.id
    });
    console.log('普通管理员创建完成');

    // 创建测试用户
    const testUsers = [];
    for (let i = 1; i <= 5; i++) {
      const user = await User.create({
        username: `testuser${i}`,
        phone: `1380000000${i + 1}`,
        email: `user${i}@test.com`,
        password_hash: 'test123456',
        role: 'user',
        phone_verified: true,
        contact_wechat: `wechat_user${i}`,
        contact_qq: `12345678${i}`
      });
      testUsers.push(user);
    }
    console.log('测试用户创建完成');

    // 创建图书分类
    const categories = [
      {
        name: '教材教辅',
        description: '各类教材和教辅资料',
        sort_order: 1
      },
      {
        name: '文学小说',
        description: '文学作品和小说类图书',
        sort_order: 2
      },
      {
        name: '科技计算机',
        description: '科技和计算机相关图书',
        sort_order: 3
      },
      {
        name: '经济管理',
        description: '经济学和管理学图书',
        sort_order: 4
      },
      {
        name: '历史传记',
        description: '历史和人物传记类图书',
        sort_order: 5
      },
      {
        name: '艺术设计',
        description: '艺术和设计类图书',
        sort_order: 6
      }
    ];

    const createdCategories = [];
    for (const categoryData of categories) {
      const category = await Category.create(categoryData);
      createdCategories.push(category);
    }
    console.log('图书分类创建完成');

    // 创建子分类
    const subCategories = [
      {
        name: '高等数学',
        description: '高等数学教材',
        parent_id: createdCategories[0].id,
        sort_order: 1
      },
      {
        name: '英语教材',
        description: '英语学习教材',
        parent_id: createdCategories[0].id,
        sort_order: 2
      },
      {
        name: '编程语言',
        description: '各种编程语言图书',
        parent_id: createdCategories[2].id,
        sort_order: 1
      },
      {
        name: '数据结构',
        description: '数据结构与算法',
        parent_id: createdCategories[2].id,
        sort_order: 2
      }
    ];

    for (const subCategoryData of subCategories) {
      await Category.create(subCategoryData);
    }
    console.log('子分类创建完成');

    // 创建示例图书
    const books = [
      {
        title: '高等数学（第七版）上册',
        author: '同济大学数学系',
        publisher: '高等教育出版社',
        isbn: '9787040396621',
        category_id: createdCategories[0].id,
        description: '高等数学经典教材，适合理工科学生使用',
        condition: '九成新',
        price: 35.00,
        original_price: 45.00,
        stock: 5,
        created_by: admin.id
      },
      {
        title: 'JavaScript高级程序设计（第4版）',
        author: 'Matt Frisbie',
        publisher: '人民邮电出版社',
        isbn: '9787115545381',
        category_id: createdCategories[2].id,
        description: 'JavaScript权威指南，前端开发必备',
        condition: '全新',
        price: 89.00,
        original_price: 129.00,
        stock: 3,
        created_by: admin.id
      },
      {
        title: '算法导论（第3版）',
        author: 'Thomas H.Cormen',
        publisher: '机械工业出版社',
        isbn: '9787111407010',
        category_id: createdCategories[2].id,
        description: '算法学习的经典教材',
        condition: '八成新',
        price: 75.00,
        original_price: 128.00,
        stock: 2,
        created_by: admin.id
      },
      {
        title: '三体',
        author: '刘慈欣',
        publisher: '重庆出版社',
        isbn: '9787536692930',
        category_id: createdCategories[1].id,
        description: '科幻小说经典之作',
        condition: '九成新',
        price: 18.00,
        original_price: 23.00,
        stock: 8,
        created_by: admin.id
      },
      {
        title: '经济学原理（第7版）微观经济学分册',
        author: 'N.格里高利·曼昆',
        publisher: '北京大学出版社',
        isbn: '9787301284735',
        category_id: createdCategories[3].id,
        description: '经济学入门经典教材',
        condition: '九成新',
        price: 55.00,
        original_price: 78.00,
        stock: 4,
        created_by: admin.id
      },
      {
        title: '大学英语综合教程1',
        author: '李荫华',
        publisher: '上海外语教育出版社',
        isbn: '9787544627023',
        category_id: createdCategories[0].id,
        description: '大学英语必修教材',
        condition: '八成新',
        price: 25.00,
        original_price: 35.00,
        stock: 6,
        created_by: admin.id
      },
      {
        title: '设计心理学',
        author: '唐纳德·A·诺曼',
        publisher: '中信出版社',
        isbn: '9787508663326',
        category_id: createdCategories[5].id,
        description: '设计师必读的心理学经典',
        condition: '全新',
        price: 42.00,
        original_price: 59.00,
        stock: 3,
        created_by: admin.id
      },
      {
        title: '史记（全本）',
        author: '司马迁',
        publisher: '中华书局',
        isbn: '9787101003048',
        category_id: createdCategories[4].id,
        description: '中国古代史学经典',
        condition: '九成新',
        price: 68.00,
        original_price: 98.00,
        stock: 2,
        created_by: admin.id
      }
    ];

    for (const bookData of books) {
      await Book.create(bookData);
    }
    console.log('示例图书创建完成');

    console.log('\n=== 数据库初始化完成 ===');
    console.log('超级管理员账号: 13800000000 / admin123456');
    console.log('普通管理员账号: 13800000001 / admin123456');
    console.log('测试用户账号: 13800000002-13800000006 / test123456');
    console.log('========================');

  } catch (error) {
    console.error('数据库初始化失败:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

// 如果直接运行此文件，则执行初始化
if (require.main === module) {
  seedDatabase();
}

module.exports = seedDatabase;
