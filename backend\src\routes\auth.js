const express = require('express');
const jwt = require('jsonwebtoken');
const { User } = require('../models');
const { validate, registerSchema, loginSchema } = require('../utils/validation');

const router = express.Router();

// 生成JWT令牌
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// 用户注册
router.post('/register', validate(registerSchema), async (req, res) => {
  try {
    const { phone, email, password, username } = req.body;

    // 检查手机号是否已存在
    const existingUser = await User.findOne({ where: { phone } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '手机号已被注册'
      });
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被注册'
        });
      }
    }

    // 检查用户名是否已存在（如果提供了用户名）
    if (username) {
      const existingUsername = await User.findOne({ where: { username } });
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          message: '用户名已被使用'
        });
      }
    }

    // 创建用户
    const user = await User.create({
      phone,
      email,
      username,
      password_hash: password, // 会在模型的hook中自动加密
      register_type: 'phone'
    });

    // 生成令牌
    const token = generateToken(user.id);

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user,
        token
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册失败'
    });
  }
});

// 用户登录
router.post('/login', validate(loginSchema), async (req, res) => {
  try {
    const { phone, password } = req.body;

    // 查找用户
    const user = await User.findOne({ where: { phone } });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误'
      });
    }

    // 检查账户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用'
      });
    }

    // 更新最后登录时间
    await user.update({ last_login_at: new Date() });

    // 生成令牌
    const token = generateToken(user.id);

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user,
        token
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 刷新令牌
router.post('/refresh', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '令牌缺失'
      });
    }

    // 验证令牌（即使过期也要验证）
    const decoded = jwt.verify(token, process.env.JWT_SECRET, { ignoreExpiration: true });
    
    // 查找用户
    const user = await User.findByPk(decoded.userId);
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用'
      });
    }

    // 生成新令牌
    const newToken = generateToken(user.id);

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        token: newToken
      }
    });
  } catch (error) {
    console.error('令牌刷新错误:', error);
    res.status(401).json({
      success: false,
      message: '令牌刷新失败'
    });
  }
});

// 第三方登录（微信）- 占位实现
router.post('/wechat', async (req, res) => {
  try {
    // TODO: 实现微信登录逻辑
    res.status(501).json({
      success: false,
      message: '微信登录功能暂未实现'
    });
  } catch (error) {
    console.error('微信登录错误:', error);
    res.status(500).json({
      success: false,
      message: '微信登录失败'
    });
  }
});

// 第三方登录（QQ）- 占位实现
router.post('/qq', async (req, res) => {
  try {
    // TODO: 实现QQ登录逻辑
    res.status(501).json({
      success: false,
      message: 'QQ登录功能暂未实现'
    });
  } catch (error) {
    console.error('QQ登录错误:', error);
    res.status(500).json({
      success: false,
      message: 'QQ登录失败'
    });
  }
});

module.exports = router;
