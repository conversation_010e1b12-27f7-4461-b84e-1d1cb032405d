const express = require('express');
const { Op } = require('sequelize');
const { Message, User, Book, Order } = require('../models');
const { validate, sendMessageSchema } = require('../utils/validation');

const router = express.Router();

// 发送消息
router.post('/', validate(sendMessageSchema), async (req, res) => {
  try {
    const { content, receiver_id, book_id, order_id, type } = req.body;
    const senderId = req.user.id;

    // 验证接收者是否存在（如果指定了接收者）
    if (receiver_id) {
      const receiver = await User.findByPk(receiver_id);
      if (!receiver) {
        return res.status(400).json({
          success: false,
          message: '接收者不存在'
        });
      }
    }

    // 验证图书是否存在（如果指定了图书）
    if (book_id) {
      const book = await Book.findByPk(book_id);
      if (!book) {
        return res.status(400).json({
          success: false,
          message: '图书不存在'
        });
      }
    }

    // 验证订单是否存在（如果指定了订单）
    if (order_id) {
      const order = await Order.findByPk(order_id);
      if (!order) {
        return res.status(400).json({
          success: false,
          message: '订单不存在'
        });
      }
    }

    const message = await Message.create({
      sender_id: senderId,
      receiver_id,
      book_id,
      order_id,
      content,
      type: type || 'chat'
    });

    // 获取完整的消息信息
    const fullMessage = await Message.findByPk(message.id, {
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'avatar']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'username', 'avatar']
        },
        {
          model: Book,
          as: 'book',
          attributes: ['id', 'title', 'cover_image']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_number']
        }
      ]
    });

    // 通过Socket.IO发送实时消息
    const io = req.app.get('io');
    if (receiver_id && io) {
      io.to(`user_${receiver_id}`).emit('new_message', fullMessage);
    }

    res.status(201).json({
      success: true,
      message: '消息发送成功',
      data: fullMessage
    });
  } catch (error) {
    console.error('发送消息错误:', error);
    res.status(500).json({
      success: false,
      message: '发送消息失败'
    });
  }
});

// 获取消息列表
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      book_id,
      order_id,
      conversation_with
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {
      [Op.or]: [
        { sender_id: req.user.id },
        { receiver_id: req.user.id }
      ],
      status: 'active'
    };

    // 类型筛选
    if (type) {
      where.type = type;
    }

    // 图书相关消息
    if (book_id) {
      where.book_id = book_id;
    }

    // 订单相关消息
    if (order_id) {
      where.order_id = order_id;
    }

    // 与特定用户的对话
    if (conversation_with) {
      where[Op.or] = [
        { sender_id: req.user.id, receiver_id: conversation_with },
        { sender_id: conversation_with, receiver_id: req.user.id }
      ];
    }

    const { count, rows: messages } = await Message.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'avatar']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'username', 'avatar']
        },
        {
          model: Book,
          as: 'book',
          attributes: ['id', 'title', 'cover_image']
        },
        {
          model: Order,
          as: 'order',
          attributes: ['id', 'order_number']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        messages,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取消息列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取消息列表失败'
    });
  }
});

// 获取对话列表
router.get('/conversations', async (req, res) => {
  try {
    const userId = req.user.id;

    // 获取最近的对话
    const conversations = await Message.findAll({
      where: {
        [Op.or]: [
          { sender_id: userId },
          { receiver_id: userId }
        ],
        status: 'active'
      },
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'avatar']
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'username', 'avatar']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: 50
    });

    // 按对话分组
    const conversationMap = new Map();
    
    conversations.forEach(message => {
      const otherUserId = message.sender_id === userId ? message.receiver_id : message.sender_id;
      const otherUser = message.sender_id === userId ? message.receiver : message.sender;
      
      if (!conversationMap.has(otherUserId)) {
        conversationMap.set(otherUserId, {
          user: otherUser,
          last_message: message,
          unread_count: 0
        });
      }
      
      // 计算未读消息数
      if (message.receiver_id === userId && !message.read_status) {
        conversationMap.get(otherUserId).unread_count++;
      }
    });

    const conversationList = Array.from(conversationMap.values());

    res.json({
      success: true,
      data: conversationList
    });
  } catch (error) {
    console.error('获取对话列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取对话列表失败'
    });
  }
});

// 标记消息为已读
router.patch('/:id/read', async (req, res) => {
  try {
    const message = await Message.findByPk(req.params.id);
    
    if (!message) {
      return res.status(404).json({
        success: false,
        message: '消息不存在'
      });
    }

    // 只有接收者可以标记为已读
    if (message.receiver_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    await message.update({ read_status: true });

    res.json({
      success: true,
      message: '消息已标记为已读'
    });
  } catch (error) {
    console.error('标记消息已读错误:', error);
    res.status(500).json({
      success: false,
      message: '标记消息已读失败'
    });
  }
});

// 批量标记消息为已读
router.patch('/batch/read', async (req, res) => {
  try {
    const { message_ids, conversation_with } = req.body;

    let where = {
      receiver_id: req.user.id,
      read_status: false
    };

    if (message_ids && Array.isArray(message_ids)) {
      where.id = { [Op.in]: message_ids };
    } else if (conversation_with) {
      where.sender_id = conversation_with;
    } else {
      return res.status(400).json({
        success: false,
        message: '请提供消息ID列表或对话用户ID'
      });
    }

    const [updatedCount] = await Message.update(
      { read_status: true },
      { where }
    );

    res.json({
      success: true,
      message: `成功标记 ${updatedCount} 条消息为已读`,
      data: {
        updated_count: updatedCount
      }
    });
  } catch (error) {
    console.error('批量标记消息已读错误:', error);
    res.status(500).json({
      success: false,
      message: '批量标记消息已读失败'
    });
  }
});

module.exports = router;
