const express = require('express');
const { Op } = require('sequelize');
const Favorite = require('../models/Favorite');
const Book = require('../models/Book');
const User = require('../models/User');
const Category = require('../models/Category');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取用户收藏列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, sort = 'created_at_DESC' } = req.query;
    const offset = (page - 1) * limit;
    
    // 解析排序参数
    const [sortField, sortOrder] = sort.split('_');
    const order = [[sortField, sortOrder.toUpperCase()]];

    const { count, rows: favorites } = await Favorite.findAndCountAll({
      where: {
        user_id: req.user.id
      },
      include: [
        {
          model: Book,
          as: 'book',
          include: [
            {
              model: Category,
              as: 'category'
            },
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'username', 'phone']
            }
          ]
        }
      ],
      order,
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        favorites,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('获取收藏列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收藏列表失败'
    });
  }
});

// 添加收藏
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { book_id } = req.body;

    if (!book_id) {
      return res.status(400).json({
        success: false,
        message: '图书ID不能为空'
      });
    }

    // 检查图书是否存在
    const book = await Book.findByPk(book_id);
    if (!book) {
      return res.status(404).json({
        success: false,
        message: '图书不存在'
      });
    }

    // 检查是否已经收藏
    const existingFavorite = await Favorite.findOne({
      where: {
        user_id: req.user.id,
        book_id
      }
    });

    if (existingFavorite) {
      return res.status(409).json({
        success: false,
        message: '已经收藏过这本图书'
      });
    }

    // 创建收藏记录
    const favorite = await Favorite.create({
      user_id: req.user.id,
      book_id
    });

    // 返回完整的收藏信息
    const favoriteWithBook = await Favorite.findByPk(favorite.id, {
      include: [
        {
          model: Book,
          as: 'book',
          include: [
            {
              model: Category,
              as: 'category'
            },
            {
              model: User,
              as: 'creator',
              attributes: ['id', 'username', 'phone']
            }
          ]
        }
      ]
    });

    logger.info(`用户 ${req.user.id} 收藏了图书 ${book_id}`);

    res.status(201).json({
      success: true,
      data: favoriteWithBook,
      message: '收藏成功'
    });
  } catch (error) {
    logger.error('添加收藏失败:', error);
    res.status(500).json({
      success: false,
      message: '添加收藏失败'
    });
  }
});

// 取消收藏
router.delete('/:book_id', authenticateToken, async (req, res) => {
  try {
    const { book_id } = req.params;

    const favorite = await Favorite.findOne({
      where: {
        user_id: req.user.id,
        book_id
      }
    });

    if (!favorite) {
      return res.status(404).json({
        success: false,
        message: '收藏记录不存在'
      });
    }

    await favorite.destroy();

    logger.info(`用户 ${req.user.id} 取消收藏图书 ${book_id}`);

    res.json({
      success: true,
      message: '取消收藏成功'
    });
  } catch (error) {
    logger.error('取消收藏失败:', error);
    res.status(500).json({
      success: false,
      message: '取消收藏失败'
    });
  }
});

// 检查是否已收藏
router.get('/check/:book_id', authenticateToken, async (req, res) => {
  try {
    const { book_id } = req.params;

    const favorite = await Favorite.findOne({
      where: {
        user_id: req.user.id,
        book_id
      }
    });

    res.json({
      success: true,
      data: {
        is_favorited: !!favorite,
        favorite_id: favorite ? favorite.id : null
      }
    });
  } catch (error) {
    logger.error('检查收藏状态失败:', error);
    res.status(500).json({
      success: false,
      message: '检查收藏状态失败'
    });
  }
});

// 批量操作收藏
router.post('/batch', authenticateToken, async (req, res) => {
  try {
    const { action, book_ids } = req.body;

    if (!action || !book_ids || !Array.isArray(book_ids)) {
      return res.status(400).json({
        success: false,
        message: '参数错误'
      });
    }

    if (action === 'add') {
      // 批量添加收藏
      const favorites = [];
      for (const book_id of book_ids) {
        // 检查是否已存在
        const existing = await Favorite.findOne({
          where: {
            user_id: req.user.id,
            book_id
          }
        });

        if (!existing) {
          favorites.push({
            user_id: req.user.id,
            book_id
          });
        }
      }

      if (favorites.length > 0) {
        await Favorite.bulkCreate(favorites);
      }

      res.json({
        success: true,
        data: {
          added_count: favorites.length,
          skipped_count: book_ids.length - favorites.length
        },
        message: `成功添加 ${favorites.length} 个收藏`
      });
    } else if (action === 'remove') {
      // 批量删除收藏
      const result = await Favorite.destroy({
        where: {
          user_id: req.user.id,
          book_id: {
            [Op.in]: book_ids
          }
        }
      });

      res.json({
        success: true,
        data: {
          removed_count: result
        },
        message: `成功删除 ${result} 个收藏`
      });
    } else {
      res.status(400).json({
        success: false,
        message: '不支持的操作类型'
      });
    }
  } catch (error) {
    logger.error('批量操作收藏失败:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败'
    });
  }
});

// 获取收藏统计
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const totalCount = await Favorite.count({
      where: {
        user_id: req.user.id
      }
    });

    // 按分类统计
    const categoryStats = await Favorite.findAll({
      where: {
        user_id: req.user.id
      },
      include: [
        {
          model: Book,
          as: 'book',
          include: [
            {
              model: Category,
              as: 'category'
            }
          ]
        }
      ],
      attributes: []
    });

    const categoryCount = {};
    categoryStats.forEach(favorite => {
      const categoryName = favorite.book?.category?.name || '未分类';
      categoryCount[categoryName] = (categoryCount[categoryName] || 0) + 1;
    });

    res.json({
      success: true,
      data: {
        total_count: totalCount,
        category_stats: categoryCount
      }
    });
  } catch (error) {
    logger.error('获取收藏统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收藏统计失败'
    });
  }
});

module.exports = router;
