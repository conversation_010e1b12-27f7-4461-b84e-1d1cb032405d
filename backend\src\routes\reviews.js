const express = require('express');
const { Op } = require('sequelize');
const Review = require('../models/Review');
const ReviewHelpful = require('../models/ReviewHelpful');
const Book = require('../models/Book');
const User = require('../models/User');
const Order = require('../models/Order');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取图书评论列表
router.get('/book/:bookId', async (req, res) => {
  try {
    const { bookId } = req.params;
    const { 
      page = 1, 
      limit = 10, 
      sort = 'created_at_DESC',
      rating,
      has_images 
    } = req.query;
    
    const offset = (page - 1) * limit;
    const [sortField, sortOrder] = sort.split('_');
    
    // 构建查询条件
    const where = {
      book_id: bookId,
      status: 'approved',
      reply_to: null // 只获取主评论，不包括回复
    };
    
    if (rating) {
      where.rating = rating;
    }
    
    if (has_images === 'true') {
      where.images = {
        [Op.not]: []
      };
    }

    const { count, rows: reviews } = await Review.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'avatar']
        },
        {
          model: Review,
          as: 'replies',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'username', 'avatar']
            }
          ]
        }
      ],
      order: [[sortField, sortOrder.toUpperCase()]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // 如果用户已登录，获取用户的点赞状态
    let userHelpfulMap = {};
    if (req.user) {
      const reviewIds = reviews.map(review => review.id);
      const userHelpful = await ReviewHelpful.findAll({
        where: {
          user_id: req.user.id,
          review_id: {
            [Op.in]: reviewIds
          }
        }
      });
      
      userHelpfulMap = userHelpful.reduce((map, helpful) => {
        map[helpful.review_id] = helpful.is_helpful;
        return map;
      }, {});
    }

    // 添加用户点赞状态
    const reviewsWithHelpful = reviews.map(review => ({
      ...review.toJSON(),
      user_helpful: userHelpfulMap[review.id] || null
    }));

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        reviews: reviewsWithHelpful,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('获取评论列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取评论列表失败'
    });
  }
});

// 获取图书评论统计
router.get('/book/:bookId/stats', async (req, res) => {
  try {
    const { bookId } = req.params;

    // 获取评论统计
    const stats = await Review.findAll({
      where: {
        book_id: bookId,
        status: 'approved',
        reply_to: null
      },
      attributes: [
        'rating',
        [sequelize.fn('COUNT', sequelize.col('rating')), 'count']
      ],
      group: ['rating'],
      raw: true
    });

    // 计算总评论数和平均评分
    const totalReviews = stats.reduce((sum, stat) => sum + parseInt(stat.count), 0);
    const totalRating = stats.reduce((sum, stat) => sum + (stat.rating * parseInt(stat.count)), 0);
    const averageRating = totalReviews > 0 ? (totalRating / totalReviews).toFixed(1) : 0;

    // 构建评分分布
    const ratingDistribution = {};
    for (let i = 1; i <= 5; i++) {
      const stat = stats.find(s => s.rating === i);
      ratingDistribution[i] = stat ? parseInt(stat.count) : 0;
    }

    res.json({
      success: true,
      data: {
        total_reviews: totalReviews,
        average_rating: parseFloat(averageRating),
        rating_distribution: ratingDistribution
      }
    });
  } catch (error) {
    logger.error('获取评论统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取评论统计失败'
    });
  }
});

// 创建评论
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      book_id,
      order_id,
      rating,
      title,
      content,
      images = [],
      is_anonymous = false,
      reply_to
    } = req.body;

    // 验证必填字段
    if (!book_id || !rating || !content) {
      return res.status(400).json({
        success: false,
        message: '图书ID、评分和评论内容不能为空'
      });
    }

    // 验证评分范围
    if (rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: '评分必须在1-5之间'
      });
    }

    // 检查图书是否存在
    const book = await Book.findByPk(book_id);
    if (!book) {
      return res.status(404).json({
        success: false,
        message: '图书不存在'
      });
    }

    // 如果是主评论（非回复），检查用户是否已经评论过
    if (!reply_to) {
      const existingReview = await Review.findOne({
        where: {
          user_id: req.user.id,
          book_id,
          reply_to: null
        }
      });

      if (existingReview) {
        return res.status(409).json({
          success: false,
          message: '您已经评论过这本图书'
        });
      }
    }

    // 如果提供了订单ID，验证订单
    if (order_id) {
      const order = await Order.findOne({
        where: {
          id: order_id,
          user_id: req.user.id,
          status: 'delivered'
        }
      });

      if (!order) {
        return res.status(400).json({
          success: false,
          message: '只能对已完成的订单进行评论'
        });
      }
    }

    // 创建评论
    const review = await Review.create({
      user_id: req.user.id,
      book_id,
      order_id,
      rating: reply_to ? null : rating, // 回复不需要评分
      title,
      content,
      images,
      is_anonymous,
      reply_to,
      status: 'approved' // 暂时自动审核通过
    });

    // 获取完整的评论信息
    const fullReview = await Review.findByPk(review.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'avatar']
        }
      ]
    });

    logger.info(`用户 ${req.user.id} 创建了评论 ${review.id}`);

    res.status(201).json({
      success: true,
      data: fullReview,
      message: '评论发布成功'
    });
  } catch (error) {
    logger.error('创建评论失败:', error);
    res.status(500).json({
      success: false,
      message: '创建评论失败'
    });
  }
});

// 点赞/取消点赞评论
router.post('/:reviewId/helpful', authenticateToken, async (req, res) => {
  try {
    const { reviewId } = req.params;
    const { is_helpful = true } = req.body;

    // 检查评论是否存在
    const review = await Review.findByPk(reviewId);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    // 检查是否已经点赞过
    const existingHelpful = await ReviewHelpful.findOne({
      where: {
        user_id: req.user.id,
        review_id: reviewId
      }
    });

    if (existingHelpful) {
      if (existingHelpful.is_helpful === is_helpful) {
        // 取消点赞
        await existingHelpful.destroy();
        
        // 更新评论的点赞数
        await review.decrement('helpful_count');
        
        res.json({
          success: true,
          data: { is_helpful: null },
          message: '已取消'
        });
      } else {
        // 更新点赞状态
        await existingHelpful.update({ is_helpful });
        
        res.json({
          success: true,
          data: { is_helpful },
          message: is_helpful ? '标记为有用' : '标记为无用'
        });
      }
    } else {
      // 新增点赞
      await ReviewHelpful.create({
        user_id: req.user.id,
        review_id: reviewId,
        is_helpful
      });

      // 更新评论的点赞数
      if (is_helpful) {
        await review.increment('helpful_count');
      }

      res.json({
        success: true,
        data: { is_helpful },
        message: is_helpful ? '标记为有用' : '标记为无用'
      });
    }
  } catch (error) {
    logger.error('点赞评论失败:', error);
    res.status(500).json({
      success: false,
      message: '操作失败'
    });
  }
});

// 删除评论（用户只能删除自己的评论）
router.delete('/:reviewId', authenticateToken, async (req, res) => {
  try {
    const { reviewId } = req.params;

    const review = await Review.findOne({
      where: {
        id: reviewId,
        user_id: req.user.id
      }
    });

    if (!review) {
      return res.status(404).json({
        success: false,
        message: '评论不存在或无权删除'
      });
    }

    await review.destroy();

    logger.info(`用户 ${req.user.id} 删除了评论 ${reviewId}`);

    res.json({
      success: true,
      message: '评论删除成功'
    });
  } catch (error) {
    logger.error('删除评论失败:', error);
    res.status(500).json({
      success: false,
      message: '删除评论失败'
    });
  }
});

// 获取用户的评论列表
router.get('/user/my', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { count, rows: reviews } = await Review.findAndCountAll({
      where: {
        user_id: req.user.id,
        reply_to: null
      },
      include: [
        {
          model: Book,
          as: 'book',
          attributes: ['id', 'title', 'cover_image', 'author']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        reviews,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('获取用户评论失败:', error);
    res.status(500).json({
      success: false,
      message: '获取评论列表失败'
    });
  }
});

module.exports = router;
