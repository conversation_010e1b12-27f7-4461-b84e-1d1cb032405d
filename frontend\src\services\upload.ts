import api from './api';
import { ApiResponse } from '../types';

export const uploadService = {
  // 上传头像
  async uploadAvatar(file: File): Promise<ApiResponse<{
    url: string;
    filename: string;
    size: number;
  }>> {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await api.post('/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 上传图书图片
  async uploadBookImages(files: File[]): Promise<ApiResponse<{
    files: Array<{
      url: string;
      filename: string;
      size: number;
      originalname: string;
    }>;
    count: number;
  }>> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('book_images', file);
    });
    
    const response = await api.post('/upload/book-images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 删除文件
  async deleteFile(filename: string, type: 'avatar' | 'book'): Promise<ApiResponse> {
    const response = await api.delete('/upload/file', {
      data: { filename, type }
    });
    return response.data;
  },

  // 获取文件信息
  async getFileInfo(type: string, filename: string): Promise<ApiResponse<{
    filename: string;
    size: number;
    created_at: string;
    modified_at: string;
    url: string;
  }>> {
    const response = await api.get(`/upload/file/${type}/${filename}`);
    return response.data;
  },

  // 批量删除文件
  async batchDeleteFiles(files: Array<{
    filename: string;
    type: 'avatar' | 'book';
  }>): Promise<ApiResponse<{
    results: Array<{
      filename: string;
      success: boolean;
      message: string;
    }>;
    success_count: number;
    total_count: number;
  }>> {
    const response = await api.delete('/upload/batch', {
      data: { files }
    });
    return response.data;
  }
};
