import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import OrderList from './OrderList';
import OrderCreate from './OrderCreate';
import OrderDetail from './OrderDetail';

const Order: React.FC = () => {
  return (
    <Routes>
      <Route index element={<OrderList />} />
      <Route path="create" element={<OrderCreate />} />
      <Route path=":id" element={<OrderDetail />} />
      <Route path="*" element={<Navigate to="/orders" replace />} />
    </Routes>
  );
};

export default Order;
