const express = require('express');
const { Op } = require('sequelize');
const { Order, OrderItem, Book, User } = require('../models');
const { validate, createOrderSchema } = require('../utils/validation');
const { requireOwnerOrAdmin } = require('../middleware/auth');

const router = express.Router();

// 创建订单
router.post('/', validate(createOrderSchema), async (req, res) => {
  const transaction = await require('../config/database').transaction();
  
  try {
    const { items, delivery_address, delivery_phone } = req.body;
    const userId = req.user.id;

    let totalAmount = 0;
    const orderItems = [];

    // 验证商品并计算总价
    for (const item of items) {
      const book = await Book.findByPk(item.book_id);
      
      if (!book) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: `图书 ${item.book_id} 不存在`
        });
      }

      if (book.status !== '上架') {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: `图书 "${book.title}" 当前不可购买`
        });
      }

      if (book.stock < item.quantity) {
        await transaction.rollback();
        return res.status(400).json({
          success: false,
          message: `图书 "${book.title}" 库存不足`
        });
      }

      const subtotal = book.price * item.quantity;
      totalAmount += subtotal;

      orderItems.push({
        book_id: item.book_id,
        quantity: item.quantity,
        price: book.price,
        subtotal
      });
    }

    // 创建订单
    const order = await Order.create({
      user_id: userId,
      total_amount: totalAmount,
      delivery_address,
      delivery_phone
    }, { transaction });

    // 创建订单项
    for (const item of orderItems) {
      await OrderItem.create({
        order_id: order.id,
        ...item
      }, { transaction });

      // 减少库存
      await Book.decrement('stock', {
        by: item.quantity,
        where: { id: item.book_id },
        transaction
      });
    }

    await transaction.commit();

    // 获取完整的订单信息
    const fullOrder = await Order.findByPk(order.id, {
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Book,
              as: 'book',
              attributes: ['id', 'title', 'cover_image']
            }
          ]
        }
      ]
    });

    res.status(201).json({
      success: true,
      message: '订单创建成功',
      data: fullOrder
    });
  } catch (error) {
    await transaction.rollback();
    console.error('创建订单错误:', error);
    res.status(500).json({
      success: false,
      message: '创建订单失败'
    });
  }
});

// 获取用户订单列表
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      start_date,
      end_date
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { user_id: req.user.id };

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 日期筛选
    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) {
        where.created_at[Op.gte] = new Date(start_date);
      }
      if (end_date) {
        where.created_at[Op.lte] = new Date(end_date);
      }
    }

    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      include: [
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Book,
              as: 'book',
              attributes: ['id', 'title', 'cover_image', 'author']
            }
          ]
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单列表失败'
    });
  }
});

// 获取订单详情
router.get('/:id', requireOwnerOrAdmin('user_id'), async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'phone']
        },
        {
          model: OrderItem,
          as: 'items',
          include: [
            {
              model: Book,
              as: 'book',
              attributes: ['id', 'title', 'cover_image', 'author', 'condition']
            }
          ]
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 检查权限
    if (req.user.role === 'user' && order.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('获取订单详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单详情失败'
    });
  }
});

// 取消订单
router.patch('/:id/cancel', async (req, res) => {
  const transaction = await require('../config/database').transaction();
  
  try {
    const order = await Order.findByPk(req.params.id, {
      include: [
        {
          model: OrderItem,
          as: 'items'
        }
      ]
    });

    if (!order) {
      await transaction.rollback();
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 检查权限
    if (req.user.role === 'user' && order.user_id !== req.user.id) {
      await transaction.rollback();
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 只有待支付状态的订单可以取消
    if (order.status !== 'pending') {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: '只有待支付的订单可以取消'
      });
    }

    // 恢复库存
    for (const item of order.items) {
      await Book.increment('stock', {
        by: item.quantity,
        where: { id: item.book_id },
        transaction
      });
    }

    // 更新订单状态
    await order.update({ status: 'cancelled' }, { transaction });

    await transaction.commit();

    res.json({
      success: true,
      message: '订单已取消'
    });
  } catch (error) {
    await transaction.rollback();
    console.error('取消订单错误:', error);
    res.status(500).json({
      success: false,
      message: '取消订单失败'
    });
  }
});

module.exports = router;
