import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout as AntdLayout } from 'antd';
import Header from './Header';
import Footer from './Footer';
import styled from 'styled-components';

const { Content } = AntdLayout;

const StyledLayout = styled(AntdLayout)`
  min-height: 100vh;
`;

const StyledContent = styled(Content)`
  padding: 24px;
  margin: 0;
  min-height: calc(100vh - 134px); // 减去header和footer的高度
  background: #f5f5f5;
`;

const Layout: React.FC = () => {
  return (
    <StyledLayout>
      <Header />
      <StyledContent>
        <Outlet />
      </StyledContent>
      <Footer />
    </StyledLayout>
  );
};

export default Layout;
