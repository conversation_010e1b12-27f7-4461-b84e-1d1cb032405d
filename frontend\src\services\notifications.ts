import api from './api';
import { ApiResponse, PaginationResponse } from '../types';

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  content: string;
  type: 'system' | 'order' | 'book' | 'review' | 'promotion' | 'security' | 'announcement';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  status: 'unread' | 'read' | 'archived';
  action_type?: string;
  action_data?: any;
  sender_id?: string;
  scheduled_at?: string;
  sent_at?: string;
  read_at?: string;
  expires_at?: string;
  is_global: boolean;
  metadata?: any;
  created_at: string;
  updated_at: string;
  sender?: {
    id: string;
    username: string;
    avatar?: string;
  };
}

export interface NotificationStats {
  total: number;
  unread: number;
  by_type: Record<string, number>;
  by_status: Record<string, number>;
}

export interface CreateNotificationData {
  user_ids: string[];
  title: string;
  content: string;
  type?: string;
  priority?: string;
  action_type?: string;
  action_data?: any;
  metadata?: any;
}

export interface CreateAnnouncementData {
  title: string;
  content: string;
  priority?: string;
  expires_at?: string;
  metadata?: any;
}

export interface CreatePromotionData {
  user_ids: string[];
  title: string;
  content: string;
  promotion_data: any;
}

export const notificationsService = {
  // 获取通知列表
  async getNotifications(params: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
    priority?: string;
  } = {}): Promise<PaginationResponse<Notification>> {
    const response = await api.get('/notifications', { params });
    return response.data;
  },

  // 获取未读通知数量
  async getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    const response = await api.get('/notifications/unread-count');
    return response.data;
  },

  // 获取通知统计
  async getStats(): Promise<ApiResponse<NotificationStats>> {
    const response = await api.get('/notifications/stats');
    return response.data;
  },

  // 标记通知为已读
  async markAsRead(notificationId: string): Promise<ApiResponse> {
    const response = await api.put(`/notifications/${notificationId}/read`);
    return response.data;
  },

  // 批量标记通知为已读
  async batchMarkAsRead(notificationIds: string[]): Promise<ApiResponse> {
    const response = await api.put('/notifications/batch/read', {
      notification_ids: notificationIds
    });
    return response.data;
  },

  // 标记所有通知为已读
  async markAllAsRead(): Promise<ApiResponse> {
    const response = await api.put('/notifications/all/read');
    return response.data;
  },

  // 删除通知
  async deleteNotification(notificationId: string): Promise<ApiResponse> {
    const response = await api.delete(`/notifications/${notificationId}`);
    return response.data;
  },

  // 发送系统通知（管理员）
  async sendSystemNotification(data: CreateNotificationData): Promise<ApiResponse<Notification[]>> {
    const response = await api.post('/notifications/system', data);
    return response.data;
  },

  // 发送全局公告（管理员）
  async sendAnnouncement(data: CreateAnnouncementData): Promise<ApiResponse<Notification[]>> {
    const response = await api.post('/notifications/announcement', data);
    return response.data;
  },

  // 发送促销通知（管理员）
  async sendPromotionNotification(data: CreatePromotionData): Promise<ApiResponse<Notification[]>> {
    const response = await api.post('/notifications/promotion', data);
    return response.data;
  },

  // 清理过期通知（管理员）
  async cleanupExpiredNotifications(): Promise<ApiResponse<{ deleted_count: number }>> {
    const response = await api.delete('/notifications/cleanup/expired');
    return response.data;
  }
};
