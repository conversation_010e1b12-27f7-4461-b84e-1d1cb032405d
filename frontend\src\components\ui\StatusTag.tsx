import React from 'react';
import { Tag } from 'antd';
import styled from 'styled-components';
import {
  BOOK_CONDITIONS,
  BOOK_STATUSES,
  ORDER_STATUSES,
  PAYMENT_STATUSES,
  USER_STATUSES,
  USER_ROLES
} from '../../utils/constants';

interface StatusTagProps {
  type: 'book_condition' | 'book_status' | 'order_status' | 'payment_status' | 'user_status' | 'user_role';
  value: string;
  size?: 'small' | 'default';
}

const StyledTag = styled(Tag)<{ $size?: 'small' | 'default' }>`
  ${props => props.$size === 'small' && `
    font-size: 12px;
    padding: 0 6px;
    height: 20px;
    line-height: 18px;
  `}
`;

const StatusTag: React.FC<StatusTagProps> = ({ type, value, size = 'default' }) => {
  const getConfig = () => {
    let options;

    switch (type) {
      case 'book_condition':
        options = BOOK_CONDITIONS;
        break;
      case 'book_status':
        options = BOOK_STATUSES;
        break;
      case 'order_status':
        options = ORDER_STATUSES;
        break;
      case 'payment_status':
        options = PAYMENT_STATUSES;
        break;
      case 'user_status':
        options = USER_STATUSES;
        break;
      case 'user_role':
        options = USER_ROLES;
        break;
      default:
        return { color: 'default', label: value };
    }

    const config = options.find(option => option.value === value);
    return config || { color: 'default', label: value };
  };

  const config = getConfig();

  return (
    <StyledTag color={config.color} $size={size}>
      {config.label}
    </StyledTag>
  );
};

export default StatusTag;
