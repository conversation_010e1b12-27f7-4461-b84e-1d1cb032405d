import api from './api';
import { ApiResponse, User, LoginForm, RegisterForm } from '../types';

export const authService = {
  // 用户登录
  async login(data: LoginForm): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await api.post('/auth/login', data);
    return response.data;
  },

  // 用户注册
  async register(data: RegisterForm): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  // 刷新token
  async refreshToken(token: string): Promise<ApiResponse<{ token: string }>> {
    const response = await api.post('/auth/refresh', { token });
    return response.data;
  },

  // 微信登录
  async wechatLogin(code: string): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await api.post('/auth/wechat', { code });
    return response.data;
  },

  // QQ登录
  async qqLogin(code: string): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await api.post('/auth/qq', { code });
    return response.data;
  },

  // 登出（清除本地存储）
  logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  // 获取当前用户信息
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // 获取token
  getToken(): string | null {
    return localStorage.getItem('token');
  },

  // 保存用户信息和token
  saveAuth(user: User, token: string): void {
    localStorage.setItem('user', JSON.stringify(user));
    localStorage.setItem('token', token);
  },

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!this.getToken();
  },

  // 检查是否是管理员
  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'admin' || user?.role === 'super_admin';
  },

  // 检查是否是超级管理员
  isSuperAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'super_admin';
  }
};
