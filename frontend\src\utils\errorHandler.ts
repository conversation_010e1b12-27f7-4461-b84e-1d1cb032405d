import { message, notification } from 'antd';
import { AxiosError } from 'axios';

// 错误类型定义
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp?: string;
}

// 错误级别
export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 错误处理配置
interface ErrorHandlerConfig {
  showNotification?: boolean;
  showMessage?: boolean;
  logToConsole?: boolean;
  reportToServer?: boolean;
  level?: ErrorLevel;
}

// 默认配置
const defaultConfig: ErrorHandlerConfig = {
  showNotification: false,
  showMessage: true,
  logToConsole: true,
  reportToServer: false,
  level: ErrorLevel.ERROR
};

// 错误消息映射
const errorMessages: Record<string, string> = {
  // 网络错误
  'NETWORK_ERROR': '网络连接失败，请检查网络设置',
  'TIMEOUT_ERROR': '请求超时，请稍后重试',
  'CONNECTION_REFUSED': '无法连接到服务器',
  
  // 认证错误
  'UNAUTHORIZED': '登录已过期，请重新登录',
  'FORBIDDEN': '权限不足，无法执行此操作',
  'TOKEN_EXPIRED': '登录已过期，请重新登录',
  'INVALID_CREDENTIALS': '用户名或密码错误',
  
  // 业务错误
  'USER_NOT_FOUND': '用户不存在',
  'BOOK_NOT_FOUND': '图书不存在',
  'ORDER_NOT_FOUND': '订单不存在',
  'INSUFFICIENT_STOCK': '库存不足',
  'DUPLICATE_PHONE': '手机号已被注册',
  'DUPLICATE_EMAIL': '邮箱已被注册',
  'INVALID_PHONE': '手机号格式不正确',
  'INVALID_EMAIL': '邮箱格式不正确',
  'WEAK_PASSWORD': '密码强度不够',
  
  // 文件上传错误
  'FILE_TOO_LARGE': '文件大小超出限制',
  'INVALID_FILE_TYPE': '不支持的文件类型',
  'UPLOAD_FAILED': '文件上传失败',
  
  // 服务器错误
  'INTERNAL_SERVER_ERROR': '服务器内部错误，请稍后重试',
  'SERVICE_UNAVAILABLE': '服务暂时不可用，请稍后重试',
  'DATABASE_ERROR': '数据库错误，请稍后重试',
  
  // 验证错误
  'VALIDATION_ERROR': '数据验证失败',
  'MISSING_REQUIRED_FIELD': '缺少必填字段',
  'INVALID_FORMAT': '数据格式不正确',
  
  // 默认错误
  'UNKNOWN_ERROR': '未知错误，请稍后重试'
};

// 获取友好的错误消息
export const getFriendlyErrorMessage = (error: any): string => {
  if (typeof error === 'string') {
    return errorMessages[error] || error;
  }
  
  if (error?.code && errorMessages[error.code]) {
    return errorMessages[error.code];
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return errorMessages.UNKNOWN_ERROR;
};

// 解析 Axios 错误
export const parseAxiosError = (error: AxiosError): ApiError => {
  if (error.response) {
    // 服务器响应错误
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return {
          code: 'VALIDATION_ERROR',
          message: (data as any)?.message || '请求参数错误'
        };
      case 401:
        return {
          code: 'UNAUTHORIZED',
          message: '登录已过期，请重新登录'
        };
      case 403:
        return {
          code: 'FORBIDDEN',
          message: '权限不足'
        };
      case 404:
        return {
          code: 'NOT_FOUND',
          message: '请求的资源不存在'
        };
      case 409:
        return {
          code: (data as any)?.code || 'CONFLICT',
          message: (data as any)?.message || '数据冲突'
        };
      case 422:
        return {
          code: 'VALIDATION_ERROR',
          message: (data as any)?.message || '数据验证失败',
          details: (data as any)?.details
        };
      case 429:
        return {
          code: 'RATE_LIMIT',
          message: '请求过于频繁，请稍后重试'
        };
      case 500:
        return {
          code: 'INTERNAL_SERVER_ERROR',
          message: '服务器内部错误'
        };
      case 502:
      case 503:
      case 504:
        return {
          code: 'SERVICE_UNAVAILABLE',
          message: '服务暂时不可用'
        };
      default:
        return {
          code: 'HTTP_ERROR',
          message: (data as any)?.message || `HTTP ${status} 错误`
        };
    }
  } else if (error.request) {
    // 网络错误
    if (error.code === 'ECONNABORTED') {
      return {
        code: 'TIMEOUT_ERROR',
        message: '请求超时'
      };
    } else if (error.code === 'ECONNREFUSED') {
      return {
        code: 'CONNECTION_REFUSED',
        message: '无法连接到服务器'
      };
    } else {
      return {
        code: 'NETWORK_ERROR',
        message: '网络连接失败'
      };
    }
  } else {
    // 其他错误
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message || '未知错误'
    };
  }
};

// 错误处理器
export class ErrorHandler {
  private static instance: ErrorHandler;
  
  private constructor() {}
  
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }
  
  // 处理错误
  public handle(error: any, config: ErrorHandlerConfig = {}): void {
    const finalConfig = { ...defaultConfig, ...config };
    
    let apiError: ApiError;
    
    if (error.isAxiosError) {
      apiError = parseAxiosError(error as AxiosError);
    } else if (error.code || error.message) {
      apiError = {
        code: error.code || 'UNKNOWN_ERROR',
        message: error.message || '未知错误',
        details: error.details
      };
    } else {
      apiError = {
        code: 'UNKNOWN_ERROR',
        message: typeof error === 'string' ? error : '未知错误'
      };
    }
    
    // 控制台日志
    if (finalConfig.logToConsole) {
      console.error('[ErrorHandler]', apiError, error);
    }
    
    // 显示消息
    if (finalConfig.showMessage) {
      const friendlyMessage = getFriendlyErrorMessage(apiError);
      
      if (finalConfig.level === ErrorLevel.WARNING) {
        message.warning(friendlyMessage);
      } else {
        message.error(friendlyMessage);
      }
    }
    
    // 显示通知
    if (finalConfig.showNotification) {
      const friendlyMessage = getFriendlyErrorMessage(apiError);
      
      notification.error({
        message: '操作失败',
        description: friendlyMessage,
        duration: 5
      });
    }
    
    // 上报到服务器
    if (finalConfig.reportToServer) {
      this.reportError(apiError, error);
    }
    
    // 特殊处理
    this.handleSpecialCases(apiError);
  }
  
  // 处理特殊情况
  private handleSpecialCases(error: ApiError): void {
    switch (error.code) {
      case 'UNAUTHORIZED':
      case 'TOKEN_EXPIRED':
        // 清除本地存储的认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        
        // 跳转到登录页
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
        break;
        
      case 'FORBIDDEN':
        // 权限不足，可能需要跳转到无权限页面
        break;
        
      case 'SERVICE_UNAVAILABLE':
        // 服务不可用，可能需要显示维护页面
        break;
    }
  }
  
  // 上报错误到服务器
  private async reportError(apiError: ApiError, originalError: any): Promise<void> {
    try {
      // 这里可以调用错误上报API
      // await api.post('/api/errors/report', {
      //   error: apiError,
      //   userAgent: navigator.userAgent,
      //   url: window.location.href,
      //   timestamp: new Date().toISOString(),
      //   stack: originalError.stack
      // });
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    }
  }
}

// 导出单例实例
export const errorHandler = ErrorHandler.getInstance();

// 便捷方法
export const handleError = (error: any, config?: ErrorHandlerConfig) => {
  errorHandler.handle(error, config);
};

export const handleApiError = (error: any) => {
  errorHandler.handle(error, {
    showMessage: true,
    logToConsole: true,
    reportToServer: process.env.NODE_ENV === 'production'
  });
};

export const handleCriticalError = (error: any) => {
  errorHandler.handle(error, {
    showNotification: true,
    showMessage: false,
    logToConsole: true,
    reportToServer: true,
    level: ErrorLevel.CRITICAL
  });
};
