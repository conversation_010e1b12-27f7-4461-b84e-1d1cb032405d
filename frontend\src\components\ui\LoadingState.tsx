import React from 'react';
import { Spin, Empty, Result, Button } from 'antd';
import { LoadingOutlined, ReloadOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const LoadingContainer = styled.div<{ height?: string; minHeight?: string }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: ${props => props.height || 'auto'};
  min-height: ${props => props.minHeight || '200px'};
  padding: 40px 20px;
  text-align: center;
`;

const CustomSpin = styled(Spin)`
  .ant-spin-dot {
    font-size: 24px;
  }
  
  .ant-spin-text {
    margin-top: 12px;
    color: #666;
    font-size: 14px;
  }
`;

interface LoadingStateProps {
  loading?: boolean;
  error?: string | null;
  empty?: boolean;
  data?: any;
  height?: string;
  minHeight?: string;
  loadingText?: string;
  emptyText?: string;
  emptyDescription?: string;
  errorTitle?: string;
  errorSubTitle?: string;
  showRetry?: boolean;
  onRetry?: () => void;
  children?: React.ReactNode;
  emptyImage?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const LoadingState: React.FC<LoadingStateProps> = ({
  loading = false,
  error = null,
  empty = false,
  data,
  height,
  minHeight = '200px',
  loadingText = '加载中...',
  emptyText = '暂无数据',
  emptyDescription = '当前没有可显示的内容',
  errorTitle = '加载失败',
  errorSubTitle = '请检查网络连接或稍后重试',
  showRetry = true,
  onRetry,
  children,
  emptyImage,
  className,
  style
}) => {
  // 加载状态
  if (loading) {
    return (
      <LoadingContainer 
        height={height} 
        minHeight={minHeight}
        className={className}
        style={style}
      >
        <CustomSpin 
          size="large" 
          tip={loadingText}
          indicator={<LoadingOutlined spin />}
        />
      </LoadingContainer>
    );
  }

  // 错误状态
  if (error) {
    return (
      <LoadingContainer 
        height={height} 
        minHeight={minHeight}
        className={className}
        style={style}
      >
        <Result
          status="error"
          title={errorTitle}
          subTitle={errorSubTitle}
          extra={
            showRetry && onRetry ? (
              <Button 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={onRetry}
              >
                重新加载
              </Button>
            ) : null
          }
        />
      </LoadingContainer>
    );
  }

  // 空数据状态
  if (empty || (Array.isArray(data) && data.length === 0) || (!data && !children)) {
    return (
      <LoadingContainer 
        height={height} 
        minHeight={minHeight}
        className={className}
        style={style}
      >
        <Empty
          image={emptyImage}
          description={
            <div>
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>
                {emptyText}
              </div>
              <div style={{ fontSize: '14px', color: '#999' }}>
                {emptyDescription}
              </div>
            </div>
          }
        />
      </LoadingContainer>
    );
  }

  // 正常状态，显示子组件
  return <>{children}</>;
};

// 预设的加载状态组件
export const PageLoading: React.FC<{ tip?: string }> = ({ tip = '页面加载中...' }) => (
  <LoadingState loading={true} loadingText={tip} minHeight="60vh" />
);

export const CardLoading: React.FC<{ tip?: string }> = ({ tip = '加载中...' }) => (
  <LoadingState loading={true} loadingText={tip} minHeight="120px" />
);

export const ListLoading: React.FC<{ tip?: string }> = ({ tip = '加载列表中...' }) => (
  <LoadingState loading={true} loadingText={tip} minHeight="200px" />
);

export const DataEmpty: React.FC<{ 
  text?: string; 
  description?: string;
  action?: React.ReactNode;
}> = ({ 
  text = '暂无数据', 
  description = '当前没有可显示的内容',
  action
}) => (
  <LoadingState 
    empty={true} 
    emptyText={text} 
    emptyDescription={description}
    children={action}
  />
);

export const DataError: React.FC<{
  title?: string;
  subTitle?: string;
  onRetry?: () => void;
}> = ({
  title = '加载失败',
  subTitle = '请检查网络连接或稍后重试',
  onRetry
}) => (
  <LoadingState 
    error="error"
    errorTitle={title}
    errorSubTitle={subTitle}
    onRetry={onRetry}
  />
);

export default LoadingState;
