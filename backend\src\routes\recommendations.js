const express = require('express');
const recommendationService = require('../services/recommendationService');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取个性化推荐（需要登录）
router.get('/personalized', authenticateToken, async (req, res) => {
  try {
    const { limit = 10, type = 'hybrid' } = req.query;
    const userId = req.user.id;

    let recommendations = [];

    switch (type) {
      case 'collaborative':
        recommendations = await recommendationService.getCollaborativeRecommendations(
          userId, 
          parseInt(limit)
        );
        break;
      case 'content':
        recommendations = await recommendationService.getContentBasedRecommendations(
          userId, 
          parseInt(limit)
        );
        break;
      case 'hybrid':
      default:
        recommendations = await recommendationService.getHybridRecommendations(
          userId, 
          parseInt(limit)
        );
        break;
    }

    res.json({
      success: true,
      data: {
        books: recommendations,
        type,
        user_id: userId
      }
    });
  } catch (error) {
    logger.error('获取个性化推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

// 获取热门推荐（无需登录）
router.get('/popular', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const books = await recommendationService.getPopularBooks(parseInt(limit));

    res.json({
      success: true,
      data: {
        books,
        type: 'popular'
      }
    });
  } catch (error) {
    logger.error('获取热门推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

// 获取新书推荐
router.get('/new', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const books = await recommendationService.getNewBooks(parseInt(limit));

    res.json({
      success: true,
      data: {
        books,
        type: 'new'
      }
    });
  } catch (error) {
    logger.error('获取新书推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

// 获取相似图书推荐
router.get('/similar/:bookId', async (req, res) => {
  try {
    const { bookId } = req.params;
    const { limit = 6 } = req.query;
    
    const books = await recommendationService.getSimilarBooks(
      bookId, 
      parseInt(limit)
    );

    res.json({
      success: true,
      data: {
        books,
        type: 'similar',
        book_id: bookId
      }
    });
  } catch (error) {
    logger.error('获取相似图书推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

// 获取综合推荐（包含多种类型）
router.get('/mixed', async (req, res) => {
  try {
    const userId = req.user?.id;
    const { limit = 20 } = req.query;
    
    const sectionLimit = Math.ceil(parseInt(limit) / 4);
    
    const [popular, newBooks, personalized, trending] = await Promise.all([
      recommendationService.getPopularBooks(sectionLimit),
      recommendationService.getNewBooks(sectionLimit),
      userId ? recommendationService.getHybridRecommendations(userId, sectionLimit) : [],
      recommendationService.getPopularBooks(sectionLimit) // 可以替换为趋势算法
    ]);

    const sections = [
      {
        title: '热门推荐',
        type: 'popular',
        books: popular
      },
      {
        title: '新书上架',
        type: 'new',
        books: newBooks
      }
    ];

    if (userId && personalized.length > 0) {
      sections.push({
        title: '为您推荐',
        type: 'personalized',
        books: personalized
      });
    }

    sections.push({
      title: '热销榜单',
      type: 'trending',
      books: trending
    });

    res.json({
      success: true,
      data: {
        sections,
        user_id: userId
      }
    });
  } catch (error) {
    logger.error('获取综合推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

// 获取基于分类的推荐
router.get('/category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { limit = 10 } = req.query;
    
    const { Book, Category, User } = require('../models');
    const { Op } = require('sequelize');
    
    const books = await Book.findAll({
      where: {
        category_id: categoryId,
        status: '上架',
        stock: {
          [Op.gt]: 0
        }
      },
      include: [
        {
          model: Category,
          as: 'category'
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ],
      order: [
        ['sales_count', 'DESC'],
        ['views', 'DESC'],
        ['created_at', 'DESC']
      ],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        books,
        type: 'category',
        category_id: categoryId
      }
    });
  } catch (error) {
    logger.error('获取分类推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

// 获取基于作者的推荐
router.get('/author/:author', async (req, res) => {
  try {
    const { author } = req.params;
    const { limit = 10 } = req.query;
    
    const { Book, Category, User } = require('../models');
    const { Op } = require('sequelize');
    
    const books = await Book.findAll({
      where: {
        author: {
          [Op.iLike]: `%${author}%`
        },
        status: '上架',
        stock: {
          [Op.gt]: 0
        }
      },
      include: [
        {
          model: Category,
          as: 'category'
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }
      ],
      order: [
        ['sales_count', 'DESC'],
        ['views', 'DESC'],
        ['created_at', 'DESC']
      ],
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: {
        books,
        type: 'author',
        author
      }
    });
  } catch (error) {
    logger.error('获取作者推荐失败:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

// 记录用户行为（用于改进推荐算法）
router.post('/behavior', authenticateToken, async (req, res) => {
  try {
    const { book_id, action, duration } = req.body;
    const userId = req.user.id;

    // 这里可以记录用户行为到数据库或缓存
    // 用于改进推荐算法
    logger.info(`用户行为记录: 用户${userId} 对图书${book_id} 执行${action}操作, 持续时间${duration}ms`);

    res.json({
      success: true,
      message: '行为记录成功'
    });
  } catch (error) {
    logger.error('记录用户行为失败:', error);
    res.status(500).json({
      success: false,
      message: '记录失败'
    });
  }
});

module.exports = router;
