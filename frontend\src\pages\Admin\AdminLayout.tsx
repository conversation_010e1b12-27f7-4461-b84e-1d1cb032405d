import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Layout, Menu, Typography, Breadcrumb, Button } from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  BookOutlined,
  ShoppingOutlined,
  TagsOutlined,
  Bar<PERSON><PERSON>Outlined,
  SettingOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

const AdminContainer = styled(Layout)`
  min-height: 100vh;
`;

const AdminHeader = styled(Header)`
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const AdminSider = styled(Sider)`
  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
  }
  
  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    font-weight: bold;
    border-bottom: 1px solid #001529;
  }
`;

const AdminContent = styled(Content)`
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 112px);
`;

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.includes('/admin/overview')) return 'overview';
    if (path.includes('/admin/users')) return 'users';
    if (path.includes('/admin/books')) return 'books';
    if (path.includes('/admin/orders')) return 'orders';
    if (path.includes('/admin/categories')) return 'categories';
    return 'overview';
  };

  const getBreadcrumbItems = () => {
    const path = location.pathname;
    const items = [
      { title: '管理后台' }
    ];
    
    if (path.includes('/admin/overview')) {
      items.push({ title: '数据概览' });
    } else if (path.includes('/admin/users')) {
      items.push({ title: '用户管理' });
    } else if (path.includes('/admin/books')) {
      items.push({ title: '图书管理' });
    } else if (path.includes('/admin/orders')) {
      items.push({ title: '订单管理' });
    } else if (path.includes('/admin/categories')) {
      items.push({ title: '分类管理' });
    }
    
    return items;
  };

  const menuItems = [
    {
      key: 'overview',
      icon: <DashboardOutlined />,
      label: '数据概览',
      onClick: () => navigate('/admin/overview')
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: '用户管理',
      onClick: () => navigate('/admin/users')
    },
    {
      key: 'books',
      icon: <BookOutlined />,
      label: '图书管理',
      onClick: () => navigate('/admin/books')
    },
    {
      key: 'orders',
      icon: <ShoppingOutlined />,
      label: '订单管理',
      onClick: () => navigate('/admin/orders')
    },
    {
      key: 'categories',
      icon: <TagsOutlined />,
      label: '分类管理',
      onClick: () => navigate('/admin/categories')
    }
  ];

  return (
    <AdminContainer>
      <AdminSider width={200} theme="dark">
        <div className="logo">
          管理后台
        </div>
        <Menu
          mode="inline"
          theme="dark"
          selectedKeys={[getSelectedKey()]}
          items={menuItems}
          style={{ flex: 1, borderRight: 0 }}
        />
      </AdminSider>
      
      <Layout>
        <AdminHeader>
          <div>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/')}
              style={{ marginRight: 16 }}
            >
              返回前台
            </Button>
            <Breadcrumb items={getBreadcrumbItems()} />
          </div>
          <Title level={4} style={{ margin: 0 }}>
            收书卖书管理系统
          </Title>
        </AdminHeader>
        
        <AdminContent>
          {children}
        </AdminContent>
      </Layout>
    </AdminContainer>
  );
};

export default AdminLayout;
