import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Tag,
  Button,
  Space,
  Typography,
  Image,
  Steps,
  Timeline,
  Spin,
  message,
  Popconfirm
} from 'antd';
import {
  ArrowLeftOutlined,
  PayCircleOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  PrinterOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import dayjs from 'dayjs';
import { Order, OrderItem } from '../../types';
import { ordersService } from '../../services/orders';

const { Title, Text } = Typography;
const { Step } = Steps;

const DetailContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
`;

const OrderItems = styled.div`
  .item-row {
    display: flex;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f5f5f5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .item-image {
      width: 80px;
      height: 100px;
      margin-right: 16px;
      border-radius: 4px;
    }
    
    .item-info {
      flex: 1;
      
      .item-title {
        font-weight: 500;
        font-size: 16px;
        margin-bottom: 8px;
      }
      
      .item-details {
        color: #8c8c8c;
        font-size: 14px;
        margin-bottom: 4px;
      }
    }
    
    .item-price {
      text-align: right;
      min-width: 120px;
      
      .price {
        color: #f5222d;
        font-weight: 500;
        font-size: 16px;
      }
      
      .quantity {
        color: #8c8c8c;
        font-size: 14px;
        margin-top: 4px;
      }
      
      .subtotal {
        font-weight: bold;
        margin-top: 4px;
      }
    }
  }
`;

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      loadOrderDetail(id);
    }
  }, [id]);

  const loadOrderDetail = async (orderId: string) => {
    try {
      setLoading(true);
      const response = await ordersService.getOrder(orderId);
      if (response.success && response.data) {
        setOrder(response.data);
      } else {
        message.error('订单不存在');
        navigate('/orders');
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      message.error('加载订单详情失败');
      navigate('/orders');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelOrder = async () => {
    if (!order) return;
    
    try {
      const response = await ordersService.cancelOrder(order.id);
      if (response.success) {
        message.success('订单已取消');
        loadOrderDetail(order.id);
      }
    } catch (error) {
      console.error('取消订单失败:', error);
      message.error('取消订单失败');
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'orange', text: '待支付' },
      paid: { color: 'blue', text: '已支付' },
      delivering: { color: 'cyan', text: '配送中' },
      delivered: { color: 'green', text: '已送达' },
      return_requested: { color: 'purple', text: '退货申请' },
      returned: { color: 'default', text: '已退货' },
      cancelled: { color: 'red', text: '已取消' }
    };
    
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getOrderStep = (status: string) => {
    const stepMap: Record<string, number> = {
      pending: 0,
      paid: 1,
      delivering: 2,
      delivered: 3,
      cancelled: -1,
      returned: -1
    };
    return stepMap[status] || 0;
  };

  const renderOrderItem = (item: OrderItem) => (
    <div key={item.id} className="item-row">
      <Image
        className="item-image"
        src={item.book?.cover_image || '/images/book-placeholder.png'}
        alt={item.book?.title}
        fallback="/images/book-placeholder.png"
        preview={false}
      />
      <div className="item-info">
        <div className="item-title">{item.book?.title}</div>
        <div className="item-details">作者: {item.book?.author || '未知'}</div>
        <div className="item-details">状况: {item.book?.condition}</div>
        <div className="item-details">ISBN: {item.book?.isbn || '暂无'}</div>
      </div>
      <div className="item-price">
        <div className="price">¥{item.price}</div>
        <div className="quantity">数量: {item.quantity}</div>
        <div className="subtotal">小计: ¥{item.subtotal}</div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <DetailContainer>
        <div style={{ textAlign: 'center', padding: '64px 0' }}>
          <Spin size="large" />
        </div>
      </DetailContainer>
    );
  }

  if (!order) {
    return (
      <DetailContainer>
        <Card>
          <div style={{ textAlign: 'center', padding: '64px 0' }}>
            <Title level={3}>订单不存在</Title>
            <Button type="primary" onClick={() => navigate('/orders')}>
              返回订单列表
            </Button>
          </div>
        </Card>
      </DetailContainer>
    );
  }

  return (
    <DetailContainer>
      {/* 页面头部 */}
      <Card style={{ marginBottom: 24 }}>
        <Space style={{ marginBottom: 16 }}>
          <Button 
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/orders')}
          >
            返回订单列表
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            订单详情
          </Title>
        </Space>
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Text strong>订单号: {order.order_number}</Text>
            <span style={{ marginLeft: 16 }}>{getStatusTag(order.status)}</span>
          </div>
          <Space>
            {order.status === 'pending' && (
              <>
                <Button
                  type="primary"
                  icon={<PayCircleOutlined />}
                  onClick={() => message.info('支付功能开发中')}
                >
                  立即支付
                </Button>
                <Popconfirm
                  title="确定要取消这个订单吗？"
                  onConfirm={handleCancelOrder}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button danger icon={<DeleteOutlined />}>
                    取消订单
                  </Button>
                </Popconfirm>
              </>
            )}
            {order.status === 'delivering' && (
              <Button
                type="primary"
                icon={<CheckCircleOutlined />}
                onClick={() => message.info('确认收货功能开发中')}
              >
                确认收货
              </Button>
            )}
            <Button icon={<PrinterOutlined />}>
              打印订单
            </Button>
          </Space>
        </div>
      </Card>

      {/* 订单进度 */}
      {order.status !== 'cancelled' && order.status !== 'returned' && (
        <Card title="订单进度" style={{ marginBottom: 24 }}>
          <Steps current={getOrderStep(order.status)}>
            <Step title="提交订单" description="订单已提交" />
            <Step title="支付完成" description="等待发货" />
            <Step title="商品配送" description="配送中" />
            <Step title="确认收货" description="订单完成" />
          </Steps>
        </Card>
      )}

      {/* 订单信息 */}
      <Card title="订单信息" style={{ marginBottom: 24 }}>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="订单号">{order.order_number}</Descriptions.Item>
          <Descriptions.Item label="订单状态">{getStatusTag(order.status)}</Descriptions.Item>
          <Descriptions.Item label="下单时间">
            {dayjs(order.created_at).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="支付时间">
            {order.payment_time ? dayjs(order.payment_time).format('YYYY-MM-DD HH:mm:ss') : '未支付'}
          </Descriptions.Item>
          <Descriptions.Item label="支付方式">
            {order.payment_method === 'alipay' ? '支付宝' : 
             order.payment_method === 'wechat' ? '微信支付' : '未选择'}
          </Descriptions.Item>
          <Descriptions.Item label="配送方式">
            {order.delivery_method === 'platform' ? '平台配送' : '自提'}
          </Descriptions.Item>
          <Descriptions.Item label="配送地址" span={2}>
            {order.delivery_address || '暂无'}
          </Descriptions.Item>
          <Descriptions.Item label="联系电话">
            {order.delivery_phone || '暂无'}
          </Descriptions.Item>
          <Descriptions.Item label="订单金额">
            <Text strong style={{ color: '#f5222d', fontSize: '16px' }}>
              ¥{order.total_amount}
            </Text>
          </Descriptions.Item>
          {order.notes && (
            <Descriptions.Item label="备注信息" span={2}>
              {order.notes}
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>

      {/* 商品清单 */}
      <Card title="商品清单">
        <OrderItems>
          {order.items?.map(renderOrderItem)}
        </OrderItems>
        
        <div style={{ 
          textAlign: 'right', 
          padding: '16px 0', 
          borderTop: '1px solid #f0f0f0',
          marginTop: '16px'
        }}>
          <Space direction="vertical" align="end">
            <Text>
              共 {order.items?.reduce((sum, item) => sum + item.quantity, 0)} 件商品
            </Text>
            <Text>商品总价: ¥{order.total_amount}</Text>
            <Text>运费: 免费</Text>
            <Text strong style={{ fontSize: '18px', color: '#f5222d' }}>
              实付金额: ¥{order.total_amount}
            </Text>
          </Space>
        </div>
      </Card>
    </DetailContainer>
  );
};

export default OrderDetail;
