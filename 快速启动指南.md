# 收书卖书平台 - 快速启动指南

## 🚀 一键启动（推荐）

### 使用Docker Compose（最简单）

```bash
# 1. 克隆项目
git clone <repository-url>
cd 收书卖书

# 2. 一键部署（包含演示数据）
chmod +x deploy.sh
./deploy.sh deploy --with-demo-data

# 3. 等待服务启动完成
# 前端: http://localhost:3000
# 后端: http://localhost:3001
# 管理后台: http://localhost:3000/admin
```

### Windows用户

```cmd
# 双击运行
start.bat
```

## 📋 系统要求

- **Docker** >= 20.0
- **Docker Compose** >= 2.0
- **内存** >= 4GB
- **磁盘空间** >= 2GB

## 🔧 手动安装

### 1. 环境准备

```bash
# 安装Node.js (>= 16)
# 安装PostgreSQL (>= 13)
# 安装Redis (>= 6)
```

### 2. 后端启动

```bash
cd backend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接

# 初始化数据库
npm run db:init

# 启动开发服务器
npm run dev
```

### 3. 前端启动

```bash
cd frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置API地址

# 启动开发服务器
npm start
```

## 🎯 默认账户

### 管理员账户
- **超级管理员**: 13800000000 / admin123456
- **普通管理员**: 13800000001 / admin123456

### 测试用户
- **用户1**: 13800000002 / test123456
- **用户2**: 13800000003 / test123456
- **用户3**: 13800000004 / test123456

## 📱 功能导航

### 用户端功能
1. **首页** - http://localhost:3000
   - 图书推荐
   - 分类浏览
   - 搜索功能

2. **图书列表** - http://localhost:3000/books
   - 分类筛选
   - 价格排序
   - 条件筛选

3. **购物车** - http://localhost:3000/cart
   - 商品管理
   - 数量调整
   - 结算功能

4. **个人中心** - http://localhost:3000/profile
   - 个人信息
   - 订单管理
   - 安全设置

### 管理端功能
1. **数据概览** - http://localhost:3000/admin/overview
   - 系统统计
   - 数据图表
   - 实时监控

2. **图书管理** - http://localhost:3000/admin/books
   - 图书CRUD
   - 库存管理
   - 图片上传

3. **用户管理** - http://localhost:3000/admin/users
   - 用户列表
   - 状态管理
   - 权限控制

4. **订单管理** - http://localhost:3000/admin/orders
   - 订单处理
   - 状态更新
   - 配送管理

## 🔍 健康检查

```bash
# 检查服务状态
curl http://localhost:3001/health

# 详细健康检查
curl http://localhost:3001/health/detailed
```

## 🛠️ 常用命令

### Docker管理
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]

# 停止所有服务
docker-compose down

# 清理数据（谨慎使用）
docker-compose down -v
```

### 开发调试
```bash
# 后端日志
cd backend && npm run logs

# 前端构建
cd frontend && npm run build

# 数据库重置
cd backend && npm run db:reset
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :3001
   
   # 修改端口（在docker-compose.yml中）
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose logs postgres
   
   # 重启数据库
   docker-compose restart postgres
   ```

3. **前端无法访问后端**
   ```bash
   # 检查API地址配置
   cat frontend/.env
   
   # 检查后端服务状态
   curl http://localhost:3001/health
   ```

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
```

## 📚 开发指南

### 添加新功能
1. 后端：在`backend/src/routes/`添加路由
2. 前端：在`frontend/src/pages/`添加页面
3. 数据库：在`backend/src/database/`添加迁移

### 代码规范
- 使用TypeScript
- 遵循ESLint规则
- 组件使用函数式写法
- API使用RESTful设计

### 测试
```bash
# 后端测试
cd backend && npm test

# 前端测试
cd frontend && npm test
```

## 🚀 生产部署

### 环境配置
1. 修改`.env`文件中的生产配置
2. 配置域名和SSL证书
3. 设置数据库备份策略
4. 配置监控告警

### 部署命令
```bash
# 生产环境部署
./deploy.sh deploy

# 使用Nginx代理
docker-compose --profile production up -d
```

## 📞 技术支持

如果遇到问题，请检查：
1. 系统要求是否满足
2. 端口是否被占用
3. 环境变量是否正确配置
4. 服务日志中的错误信息

更多详细信息请参考：
- [项目总结.md](./项目总结.md)
- [项目检查报告.md](./项目检查报告.md)
- [README.md](./README.md)
