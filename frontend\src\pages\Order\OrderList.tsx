import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Tabs,
  Image,
  Popconfirm,
  message,
  Empty,
  Spin
} from 'antd';
import {
  EyeOutlined,
  DeleteOutlined,
  PayCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import dayjs from 'dayjs';
import { Order, OrderItem } from '../../types';
import { ordersService } from '../../services/orders';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const OrderContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const OrderCard = styled(Card)`
  margin-bottom: 16px;
  
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;
    
    .order-info {
      .order-number {
        font-weight: 500;
        margin-right: 16px;
      }
      
      .order-time {
        color: #8c8c8c;
        font-size: 12px;
      }
    }
  }
  
  .order-items {
    .item-row {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-image {
        width: 60px;
        height: 80px;
        margin-right: 16px;
        border-radius: 4px;
      }
      
      .item-info {
        flex: 1;
        
        .item-title {
          font-weight: 500;
          margin-bottom: 4px;
        }
        
        .item-details {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
      
      .item-price {
        text-align: right;
        
        .price {
          color: #f5222d;
          font-weight: 500;
        }
        
        .quantity {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
    }
  }
  
  .order-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
    
    .total-amount {
      font-size: 16px;
      
      .amount {
        color: #f5222d;
        font-weight: bold;
        font-size: 18px;
      }
    }
  }
`;

const OrderList: React.FC = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    loadOrders();
  }, [activeTab]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const params: any = { page: 1, limit: 50 };
      
      if (activeTab !== 'all') {
        params.status = activeTab;
      }
      
      const response = await ordersService.getOrders(params);
      if (response.success) {
        setOrders(response.data.orders || []);
      }
    } catch (error) {
      console.error('加载订单失败:', error);
      message.error('加载订单失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelOrder = async (orderId: string) => {
    try {
      const response = await ordersService.cancelOrder(orderId);
      if (response.success) {
        message.success('订单已取消');
        loadOrders();
      }
    } catch (error) {
      console.error('取消订单失败:', error);
      message.error('取消订单失败');
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      pending: { color: 'orange', text: '待支付' },
      paid: { color: 'blue', text: '已支付' },
      delivering: { color: 'cyan', text: '配送中' },
      delivered: { color: 'green', text: '已送达' },
      return_requested: { color: 'purple', text: '退货申请' },
      returned: { color: 'default', text: '已退货' },
      cancelled: { color: 'red', text: '已取消' }
    };
    
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getOrderActions = (order: Order) => {
    const actions = [];
    
    // 查看详情
    actions.push(
      <Button
        key="detail"
        type="link"
        icon={<EyeOutlined />}
        onClick={() => navigate(`/orders/${order.id}`)}
      >
        查看详情
      </Button>
    );
    
    // 支付（待支付状态）
    if (order.status === 'pending') {
      actions.push(
        <Button
          key="pay"
          type="primary"
          size="small"
          icon={<PayCircleOutlined />}
          onClick={() => message.info('支付功能开发中')}
        >
          立即支付
        </Button>
      );
    }
    
    // 确认收货（已配送状态）
    if (order.status === 'delivering') {
      actions.push(
        <Button
          key="confirm"
          type="primary"
          size="small"
          icon={<CheckCircleOutlined />}
          onClick={() => message.info('确认收货功能开发中')}
        >
          确认收货
        </Button>
      );
    }
    
    // 取消订单（待支付状态）
    if (order.status === 'pending') {
      actions.push(
        <Popconfirm
          key="cancel"
          title="确定要取消这个订单吗？"
          onConfirm={() => handleCancelOrder(order.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button
            type="link"
            danger
            size="small"
            icon={<DeleteOutlined />}
          >
            取消订单
          </Button>
        </Popconfirm>
      );
    }
    
    return actions;
  };

  const renderOrderItem = (item: OrderItem) => (
    <div key={item.id} className="item-row">
      <Image
        className="item-image"
        src={item.book?.cover_image || '/images/book-placeholder.png'}
        alt={item.book?.title}
        fallback="/images/book-placeholder.png"
        preview={false}
      />
      <div className="item-info">
        <div className="item-title">{item.book?.title}</div>
        <div className="item-details">
          作者: {item.book?.author || '未知'} | 状况: {item.book?.condition}
        </div>
      </div>
      <div className="item-price">
        <div className="price">¥{item.price}</div>
        <div className="quantity">x{item.quantity}</div>
      </div>
    </div>
  );

  const renderOrder = (order: Order) => (
    <OrderCard key={order.id}>
      <div className="order-header">
        <div className="order-info">
          <span className="order-number">订单号: {order.order_number}</span>
          {getStatusTag(order.status)}
          <div className="order-time">
            下单时间: {dayjs(order.created_at).format('YYYY-MM-DD HH:mm:ss')}
          </div>
        </div>
        <Space>
          {getOrderActions(order)}
        </Space>
      </div>
      
      <div className="order-items">
        {order.items?.map(renderOrderItem)}
      </div>
      
      <div className="order-footer">
        <div>
          <Text type="secondary">
            配送地址: {order.delivery_address || '暂无'}
          </Text>
        </div>
        <div className="total-amount">
          共 {order.items?.reduce((sum, item) => sum + item.quantity, 0)} 件商品，
          合计: <span className="amount">¥{order.total_amount}</span>
        </div>
      </div>
    </OrderCard>
  );

  return (
    <OrderContainer>
      <Card>
        <Title level={2}>我的订单</Title>
        
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="全部订单" key="all" />
          <TabPane tab="待支付" key="pending" />
          <TabPane tab="已支付" key="paid" />
          <TabPane tab="配送中" key="delivering" />
          <TabPane tab="已完成" key="delivered" />
          <TabPane tab="已取消" key="cancelled" />
        </Tabs>
        
        <Spin spinning={loading}>
          {orders.length > 0 ? (
            <div style={{ marginTop: 16 }}>
              {orders.map(renderOrder)}
            </div>
          ) : (
            <Empty
              description="暂无订单"
              style={{ margin: '64px 0' }}
            >
              <Button type="primary" onClick={() => navigate('/books')}>
                去购物
              </Button>
            </Empty>
          )}
        </Spin>
      </Card>
    </OrderContainer>
  );
};

export default OrderList;
