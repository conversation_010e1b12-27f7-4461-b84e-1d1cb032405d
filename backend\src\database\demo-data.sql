-- 演示数据脚本
-- 注意：运行此脚本前请确保已经运行了 init.sql

-- 插入演示分类数据
INSERT INTO categories (id, name, description, parent_id, sort_order, is_active, created_at, updated_at) VALUES
('cat-1', '计算机科学', '计算机相关书籍', NULL, 1, true, NOW(), NOW()),
('cat-2', '数学', '数学相关书籍', NULL, 2, true, NOW(), NOW()),
('cat-3', '英语', '英语学习书籍', NULL, 3, true, NOW(), NOW()),
('cat-4', '文学', '文学作品', NULL, 4, true, NOW(), NOW()),
('cat-5', '经济管理', '经济管理类书籍', NULL, 5, true, NOW(), NOW()),

-- 计算机科学子分类
('cat-1-1', '编程语言', 'Java、Python、JavaScript等编程语言', 'cat-1', 1, true, NOW(), NOW()),
('cat-1-2', '数据结构与算法', '数据结构和算法相关', 'cat-1', 2, true, NOW(), NOW()),
('cat-1-3', '数据库', '数据库设计与应用', 'cat-1', 3, true, NOW(), NOW()),
('cat-1-4', '人工智能', 'AI、机器学习、深度学习', 'cat-1', 4, true, NOW(), NOW()),

-- 数学子分类
('cat-2-1', '高等数学', '微积分、线性代数等', 'cat-2', 1, true, NOW(), NOW()),
('cat-2-2', '概率统计', '概率论与数理统计', 'cat-2', 2, true, NOW(), NOW()),
('cat-2-3', '离散数学', '离散数学基础', 'cat-2', 3, true, NOW(), NOW());

-- 插入演示图书数据
INSERT INTO books (id, title, author, publisher, isbn, category_id, description, price, original_price, stock, condition, status, cover_image, images, publication_date, creator_id, views, sales_count, created_at, updated_at) VALUES
-- 计算机类书籍
('book-1', 'JavaScript高级程序设计（第4版）', 'Matt Frisbie', '人民邮电出版社', '9787115545381', 'cat-1-1', 
'JavaScript技术经典名著，深入理解JavaScript语言核心特性的权威指南。', 
89.00, 109.00, 15, '九成新', '上架', 
'/images/books/js-advanced.jpg', 
'["/images/books/js-advanced-1.jpg", "/images/books/js-advanced-2.jpg"]', 
'2020-09-01', 'user-3', 156, 23, NOW(), NOW()),

('book-2', 'Python编程：从入门到实践（第2版）', 'Eric Matthes', '人民邮电出版社', '9787115546081', 'cat-1-1',
'Python编程入门经典，适合零基础学习者，包含大量实战项目。',
79.00, 99.00, 8, '八成新', '上架',
'/images/books/python-crash.jpg',
'["/images/books/python-crash-1.jpg", "/images/books/python-crash-2.jpg"]',
'2020-11-01', 'user-4', 234, 31, NOW(), NOW()),

('book-3', '算法导论（第3版）', 'Thomas H.Cormen', '机械工业出版社', '9787111407010', 'cat-1-2',
'算法领域的经典教材，计算机科学专业必读书籍。',
128.00, 158.00, 5, '全新', '上架',
'/images/books/algorithms.jpg',
'["/images/books/algorithms-1.jpg", "/images/books/algorithms-2.jpg"]',
'2012-12-01', 'user-5', 89, 12, NOW(), NOW()),

('book-4', '深入理解计算机系统（第3版）', 'Randal E.Bryant', '机械工业出版社', '9787111544937', 'cat-1',
'计算机系统经典教材，深入理解计算机系统的工作原理。',
139.00, 169.00, 3, '九成新', '上架',
'/images/books/csapp.jpg',
'["/images/books/csapp-1.jpg", "/images/books/csapp-2.jpg"]',
'2016-11-01', 'user-6', 67, 8, NOW(), NOW()),

('book-5', 'MySQL必知必会', 'Ben Forta', '人民邮电出版社', '9787115385604', 'cat-1-3',
'MySQL数据库入门经典，简洁实用的学习指南。',
39.00, 49.00, 12, '八成新', '上架',
'/images/books/mysql.jpg',
'["/images/books/mysql-1.jpg", "/images/books/mysql-2.jpg"]',
'2015-01-01', 'user-7', 178, 25, NOW(), NOW()),

-- 数学类书籍
('book-6', '高等数学（第七版）上册', '同济大学数学系', '高等教育出版社', '9787040396621', 'cat-2-1',
'高等数学经典教材，大学数学必修课程用书。',
45.00, 55.00, 20, '七成新', '上架',
'/images/books/calculus-1.jpg',
'["/images/books/calculus-1-1.jpg", "/images/books/calculus-1-2.jpg"]',
'2014-07-01', 'user-8', 145, 18, NOW(), NOW()),

('book-7', '线性代数（第六版）', '同济大学数学系', '高等教育出版社', '9787040396614', 'cat-2-1',
'线性代数标准教材，理工科学生必备。',
35.00, 42.00, 18, '八成新', '上架',
'/images/books/linear-algebra.jpg',
'["/images/books/linear-algebra-1.jpg", "/images/books/linear-algebra-2.jpg"]',
'2014-06-01', 'user-9', 123, 15, NOW(), NOW()),

('book-8', '概率论与数理统计（第四版）', '盛骤', '高等教育出版社', '9787040238969', 'cat-2-2',
'概率统计经典教材，内容全面系统。',
42.00, 52.00, 10, '九成新', '上架',
'/images/books/probability.jpg',
'["/images/books/probability-1.jpg", "/images/books/probability-2.jpg"]',
'2008-12-01', 'user-10', 98, 11, NOW(), NOW()),

-- 英语类书籍
('book-9', '新概念英语（第二册）', 'L.G.Alexander', '外语教学与研究出版社', '9787560013480', 'cat-3',
'英语学习经典教材，适合中级英语学习者。',
28.00, 35.00, 25, '八成新', '上架',
'/images/books/new-concept-2.jpg',
'["/images/books/new-concept-2-1.jpg", "/images/books/new-concept-2-2.jpg"]',
'1997-10-01', 'user-11', 189, 28, NOW(), NOW()),

('book-10', '雅思词汇胜经', '胡敏', '中国广播电视出版社', '9787504359681', 'cat-3',
'雅思考试词汇备考必备，系统全面的词汇学习。',
48.00, 58.00, 8, '九成新', '上架',
'/images/books/ielts-vocab.jpg',
'["/images/books/ielts-vocab-1.jpg", "/images/books/ielts-vocab-2.jpg"]',
'2010-03-01', 'user-12', 76, 9, NOW(), NOW()),

-- 文学类书籍
('book-11', '百年孤独', '加西亚·马尔克斯', '南海出版公司', '9787544253994', 'cat-4',
'魔幻现实主义文学代表作，诺贝尔文学奖获奖作品。',
39.50, 49.50, 6, '全新', '上架',
'/images/books/solitude.jpg',
'["/images/books/solitude-1.jpg", "/images/books/solitude-2.jpg"]',
'2011-06-01', 'user-13', 134, 16, NOW(), NOW()),

('book-12', '活着', '余华', '作家出版社', '9787506365437', 'cat-4',
'当代文学经典，深刻反映人生苦难与坚韧。',
25.00, 30.00, 14, '八成新', '上架',
'/images/books/alive.jpg',
'["/images/books/alive-1.jpg", "/images/books/alive-2.jpg"]',
'2012-08-01', 'user-14', 167, 21, NOW(), NOW()),

-- 经济管理类书籍
('book-13', '经济学原理（第7版）微观经济学分册', 'N.格里高利·曼昆', '北京大学出版社', '9787301258446', 'cat-5',
'经济学入门经典教材，诺贝尔经济学奖得主推荐。',
68.00, 78.00, 7, '九成新', '上架',
'/images/books/economics-micro.jpg',
'["/images/books/economics-micro-1.jpg", "/images/books/economics-micro-2.jpg"]',
'2015-05-01', 'user-15', 92, 10, NOW(), NOW()),

('book-14', '管理学（第13版）', '斯蒂芬·P·罗宾斯', '中国人民大学出版社', '9787300249896', 'cat-5',
'管理学经典教材，全面系统的管理理论与实践。',
75.00, 89.00, 9, '八成新', '上架',
'/images/books/management.jpg',
'["/images/books/management-1.jpg", "/images/books/management-2.jpg"]',
'2017-08-01', 'user-16', 78, 7, NOW(), NOW()),

-- 一些缺货或下架的书籍
('book-15', 'React技术栈开发实战', '朱安邦', '电子工业出版社', '9787121318542', 'cat-1-1',
'React前端开发实战指南，包含最新技术栈。',
69.00, 79.00, 0, '九成新', '缺货',
'/images/books/react-dev.jpg',
'["/images/books/react-dev-1.jpg", "/images/books/react-dev-2.jpg"]',
'2017-06-01', 'user-17', 45, 3, NOW(), NOW()),

('book-16', 'Vue.js实战', '梁灏', '清华大学出版社', '9787302456469', 'cat-1-1',
'Vue.js前端框架实战教程，适合进阶学习。',
59.00, 69.00, 2, '八成新', '下架',
'/images/books/vue-action.jpg',
'["/images/books/vue-action-1.jpg", "/images/books/vue-action-2.jpg"]',
'2017-03-01', 'user-18', 67, 5, NOW(), NOW());

-- 插入一些演示订单数据
INSERT INTO orders (id, order_number, user_id, total_amount, status, payment_status, payment_method, delivery_address, delivery_phone, delivery_method, notes, payment_time, delivery_time, created_at, updated_at) VALUES
('order-1', '202401150001', 'user-3', 89.00, 'delivered', 'paid', 'alipay', '北京市海淀区清华大学紫荆公寓1号楼101室', '13800000003', 'platform', '请放在门卫处', '2024-01-15 10:30:00', '2024-01-16 14:20:00', '2024-01-15 10:25:00', '2024-01-16 14:20:00'),
('order-2', '202401150002', 'user-4', 79.00, 'delivering', 'paid', 'wechat', '上海市浦东新区复旦大学张江校区学生宿舍2号楼205室', '13800000004', 'platform', '', '2024-01-15 14:15:00', NULL, '2024-01-15 14:10:00', '2024-01-15 14:15:00'),
('order-3', '202401150003', 'user-5', 167.00, 'paid', 'paid', 'alipay', '广州市天河区中山大学东校区学生公寓3号楼301室', '13800000005', 'platform', '周末配送', '2024-01-15 16:45:00', NULL, '2024-01-15 16:40:00', '2024-01-15 16:45:00'),
('order-4', '202401150004', 'user-6', 45.00, 'pending', 'pending', NULL, '深圳市南山区深圳大学学生宿舍4号楼401室', '13800000006', 'platform', '', NULL, NULL, '2024-01-15 18:20:00', '2024-01-15 18:20:00'),
('order-5', '202401140001', 'user-7', 128.00, 'delivered', 'paid', 'alipay', '杭州市西湖区浙江大学玉泉校区学生宿舍5号楼501室', '13800000007', 'platform', '', '2024-01-14 09:30:00', '2024-01-15 11:15:00', '2024-01-14 09:25:00', '2024-01-15 11:15:00'),
('order-6', '202401140002', 'user-8', 74.00, 'cancelled', 'pending', NULL, '南京市栖霞区南京大学仙林校区学生宿舍6号楼601室', '13800000008', 'platform', '取消订单', NULL, NULL, '2024-01-14 15:30:00', '2024-01-14 16:00:00');

-- 插入订单项数据
INSERT INTO order_items (id, order_id, book_id, quantity, price, subtotal, created_at, updated_at) VALUES
('item-1', 'order-1', 'book-1', 1, 89.00, 89.00, '2024-01-15 10:25:00', '2024-01-15 10:25:00'),
('item-2', 'order-2', 'book-2', 1, 79.00, 79.00, '2024-01-15 14:10:00', '2024-01-15 14:10:00'),
('item-3', 'order-3', 'book-3', 1, 128.00, 128.00, '2024-01-15 16:40:00', '2024-01-15 16:40:00'),
('item-4', 'order-3', 'book-5', 1, 39.00, 39.00, '2024-01-15 16:40:00', '2024-01-15 16:40:00'),
('item-5', 'order-4', 'book-6', 1, 45.00, 45.00, '2024-01-15 18:20:00', '2024-01-15 18:20:00'),
('item-6', 'order-5', 'book-3', 1, 128.00, 128.00, '2024-01-14 09:25:00', '2024-01-14 09:25:00'),
('item-7', 'order-6', 'book-7', 1, 35.00, 35.00, '2024-01-14 15:30:00', '2024-01-14 15:30:00'),
('item-8', 'order-6', 'book-5', 1, 39.00, 39.00, '2024-01-14 15:30:00', '2024-01-14 15:30:00');

-- 更新图书销量和库存
UPDATE books SET sales_count = sales_count + 1, stock = stock - 1 WHERE id = 'book-1';
UPDATE books SET sales_count = sales_count + 1, stock = stock - 1 WHERE id = 'book-2';
UPDATE books SET sales_count = sales_count + 2, stock = stock - 2 WHERE id = 'book-3';
UPDATE books SET sales_count = sales_count + 1, stock = stock - 1 WHERE id = 'book-5';
UPDATE books SET sales_count = sales_count + 1, stock = stock - 1 WHERE id = 'book-6';
UPDATE books SET sales_count = sales_count + 1, stock = stock - 1 WHERE id = 'book-7';

COMMIT;
