import api from './api';
import { ApiResponse, Category } from '../types';

export const categoriesService = {
  // 获取分类列表（树形结构）
  async getCategories(): Promise<ApiResponse<Category[]>> {
    const response = await api.get('/categories');
    return response.data;
  },

  // 获取扁平化分类列表
  async getFlatCategories(): Promise<ApiResponse<Category[]>> {
    const response = await api.get('/categories/flat');
    return response.data;
  },

  // 获取分类详情
  async getCategory(id: string): Promise<ApiResponse<Category>> {
    const response = await api.get(`/categories/${id}`);
    return response.data;
  },

  // 创建分类（管理员）
  async createCategory(data: {
    name: string;
    description?: string;
    parent_id?: string;
    sort_order?: number;
  }): Promise<ApiResponse<Category>> {
    const response = await api.post('/categories', data);
    return response.data;
  },

  // 更新分类（管理员）
  async updateCategory(id: string, data: {
    name?: string;
    description?: string;
    parent_id?: string;
    sort_order?: number;
    is_active?: boolean;
  }): Promise<ApiResponse<Category>> {
    const response = await api.put(`/categories/${id}`, data);
    return response.data;
  },

  // 删除分类（管理员）
  async deleteCategory(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/categories/${id}`);
    return response.data;
  }
};
