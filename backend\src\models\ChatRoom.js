const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ChatRoom = sequelize.define('ChatRoom', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('private', 'group', 'public'),
    defaultValue: 'private'
  },
  creator_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  book_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'books',
      key: 'id'
    }
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  status: {
    type: DataTypes.ENUM('active', 'archived', 'closed'),
    defaultValue: 'active'
  },
  max_members: {
    type: DataTypes.INTEGER,
    defaultValue: 2
  },
  settings: {
    type: DataTypes.JSONB,
    defaultValue: {}
  },
  last_message_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'chat_rooms',
  timestamps: false,
  indexes: [
    {
      fields: ['creator_id']
    },
    {
      fields: ['book_id']
    },
    {
      fields: ['order_id']
    },
    {
      fields: ['type']
    },
    {
      fields: ['status']
    },
    {
      fields: ['last_message_at']
    }
  ]
});

module.exports = ChatRoom;
