const express = require('express');
const { Op } = require('sequelize');
const { User, Book, Order, OrderItem, Category, Message } = require('../models');
const { requireAdmin, requireSuperAdmin } = require('../middleware/auth');

const router = express.Router();

// 获取系统统计信息
router.get('/stats', requireAdmin, async (req, res) => {
  try {
    // 用户统计
    const userStats = await User.findAll({
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // 图书统计
    const bookStats = await Book.findAll({
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // 订单统计
    const orderStats = await Order.findAll({
      attributes: [
        'status',
        [require('sequelize').fn('COUNT', '*'), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // 总计数据
    const totalUsers = await User.count();
    const totalBooks = await Book.count();
    const totalOrders = await Order.count();
    const totalRevenue = await Order.sum('total_amount', {
      where: { payment_status: 'paid' }
    });

    // 今日数据
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todayUsers = await User.count({
      where: {
        created_at: { [Op.gte]: today }
      }
    });

    const todayOrders = await Order.count({
      where: {
        created_at: { [Op.gte]: today }
      }
    });

    const todayRevenue = await Order.sum('total_amount', {
      where: {
        created_at: { [Op.gte]: today },
        payment_status: 'paid'
      }
    });

    res.json({
      success: true,
      data: {
        user_stats: userStats,
        book_stats: bookStats,
        order_stats: orderStats,
        totals: {
          users: totalUsers,
          books: totalBooks,
          orders: totalOrders,
          revenue: totalRevenue || 0
        },
        today: {
          users: todayUsers,
          orders: todayOrders,
          revenue: todayRevenue || 0
        }
      }
    });
  } catch (error) {
    console.error('获取系统统计信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取系统统计信息失败'
    });
  }
});

// 获取用户列表
router.get('/users', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      role,
      start_date,
      end_date
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 搜索条件
    if (search) {
      where[Op.or] = [
        { username: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 角色筛选
    if (role) {
      where.role = role;
    }

    // 日期筛选
    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) {
        where.created_at[Op.gte] = new Date(start_date);
      }
      if (end_date) {
        where.created_at[Op.lte] = new Date(end_date);
      }
    }

    const { count, rows: users } = await User.findAndCountAll({
      where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

// 更新用户状态
router.patch('/users/:id/status', requireAdmin, async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!['active', 'inactive', 'banned'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }

    const user = await User.findByPk(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 超级管理员不能被普通管理员操作
    if (user.role === 'super_admin' && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    await user.update({ status });

    res.json({
      success: true,
      message: '用户状态更新成功',
      data: user
    });
  } catch (error) {
    console.error('更新用户状态错误:', error);
    res.status(500).json({
      success: false,
      message: '更新用户状态失败'
    });
  }
});

// 获取订单列表
router.get('/orders', requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      payment_status,
      start_date,
      end_date,
      search
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 支付状态筛选
    if (payment_status) {
      where.payment_status = payment_status;
    }

    // 日期筛选
    if (start_date || end_date) {
      where.created_at = {};
      if (start_date) {
        where.created_at[Op.gte] = new Date(start_date);
      }
      if (end_date) {
        where.created_at[Op.lte] = new Date(end_date);
      }
    }

    const include = [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'username', 'phone']
      },
      {
        model: OrderItem,
        as: 'items',
        include: [
          {
            model: Book,
            as: 'book',
            attributes: ['id', 'title', 'cover_image']
          }
        ]
      }
    ];

    // 搜索条件
    if (search) {
      include[0].where = {
        [Op.or]: [
          { username: { [Op.iLike]: `%${search}%` } },
          { phone: { [Op.iLike]: `%${search}%` } }
        ]
      };
    }

    const { count, rows: orders } = await Order.findAndCountAll({
      where,
      include,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(count / limit),
          total_items: count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单列表失败'
    });
  }
});

// 更新订单状态
router.patch('/orders/:id/status', requireAdmin, async (req, res) => {
  try {
    const { status, delivery_person, delivery_person_phone } = req.body;
    
    const validStatuses = ['pending', 'paid', 'delivering', 'delivered', 'return_requested', 'returned', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }

    const order = await Order.findByPk(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    const updateData = { status };
    
    // 如果状态是配送中，记录配送信息
    if (status === 'delivering') {
      updateData.delivery_time = new Date();
      if (delivery_person) {
        updateData.delivery_person = delivery_person;
      }
      if (delivery_person_phone) {
        updateData.delivery_person_phone = delivery_person_phone;
      }
    }

    await order.update(updateData);

    res.json({
      success: true,
      message: '订单状态更新成功',
      data: order
    });
  } catch (error) {
    console.error('更新订单状态错误:', error);
    res.status(500).json({
      success: false,
      message: '更新订单状态失败'
    });
  }
});

// 创建管理员账户（仅超级管理员）
router.post('/users', requireSuperAdmin, async (req, res) => {
  try {
    const { username, email, phone, password, role = 'admin' } = req.body;

    if (!phone || !password) {
      return res.status(400).json({
        success: false,
        message: '手机号和密码是必填项'
      });
    }

    if (!['admin', 'super_admin'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: '无效的角色'
      });
    }

    // 检查手机号是否已存在
    const existingUser = await User.findOne({ where: { phone } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '手机号已被注册'
      });
    }

    const user = await User.create({
      username,
      email,
      phone,
      password_hash: password,
      role,
      created_by: req.user.id,
      register_type: 'phone'
    });

    res.status(201).json({
      success: true,
      message: '管理员账户创建成功',
      data: user
    });
  } catch (error) {
    console.error('创建管理员账户错误:', error);
    res.status(500).json({
      success: false,
      message: '创建管理员账户失败'
    });
  }
});

module.exports = router;
