import api from './api';
import { ApiResponse, User, UserStats } from '../types';

export const usersService = {
  // 获取当前用户信息
  async getProfile(): Promise<ApiResponse<User>> {
    const response = await api.get('/users/profile');
    return response.data;
  },

  // 更新用户信息
  async updateProfile(data: {
    username?: string;
    email?: string;
    avatar?: string;
    contact_wechat?: string;
    contact_qq?: string;
    contact_phone_public?: string;
  }): Promise<ApiResponse<User>> {
    const response = await api.put('/users/profile', data);
    return response.data;
  },

  // 修改密码
  async changePassword(data: {
    current_password: string;
    new_password: string;
  }): Promise<ApiResponse> {
    const response = await api.put('/users/password', data);
    return response.data;
  },

  // 获取用户统计信息
  async getUserStats(): Promise<ApiResponse<UserStats>> {
    const response = await api.get('/users/stats');
    return response.data;
  },

  // 获取用户详情（管理员功能）
  async getUser(id: string): Promise<ApiResponse<User>> {
    const response = await api.get(`/users/${id}`);
    return response.data;
  }
};
