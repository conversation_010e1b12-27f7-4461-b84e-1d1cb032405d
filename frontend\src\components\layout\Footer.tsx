import React from 'react';
import { Layout, Row, Col, Space, Typography } from 'antd';
import {
  WechatOutlined,
  QqOutlined,
  PhoneOutlined,
  MailOutlined,
  GithubOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

const { Footer: AntdFooter } = Layout;
const { Text, Link } = Typography;

const StyledFooter = styled(AntdFooter)`
  background: #001529;
  color: rgba(255, 255, 255, 0.65);
  padding: 48px 24px 24px;
`;

const FooterSection = styled.div`
  margin-bottom: 24px;
  
  h4 {
    color: #fff;
    margin-bottom: 16px;
    font-size: 16px;
  }
  
  .ant-typography {
    color: rgba(255, 255, 255, 0.65);
    
    &:hover {
      color: #1890ff;
    }
  }
`;

const Copyright = styled.div`
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.45);
`;

const Footer: React.FC = () => {
  return (
    <StyledFooter>
      <Row gutter={[32, 24]}>
        <Col xs={24} sm={12} md={6}>
          <FooterSection>
            <h4>关于我们</h4>
            <Space direction="vertical" size="small">
              <Text>收书卖书平台</Text>
              <Text>专注大学生二手书交易</Text>
              <Text>让知识传递更有价值</Text>
            </Space>
          </FooterSection>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <FooterSection>
            <h4>服务支持</h4>
            <Space direction="vertical" size="small">
              <Link href="/help">帮助中心</Link>
              <Link href="/contact">联系我们</Link>
              <Link href="/feedback">意见反馈</Link>
              <Link href="/terms">服务条款</Link>
            </Space>
          </FooterSection>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <FooterSection>
            <h4>联系方式</h4>
            <Space direction="vertical" size="small">
              <Space>
                <PhoneOutlined />
                <Text>************</Text>
              </Space>
              <Space>
                <MailOutlined />
                <Text><EMAIL></Text>
              </Space>
              <Space>
                <WechatOutlined />
                <Text>微信客服</Text>
              </Space>
            </Space>
          </FooterSection>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <FooterSection>
            <h4>关注我们</h4>
            <Space size="large">
              <WechatOutlined style={{ fontSize: '24px', cursor: 'pointer' }} />
              <QqOutlined style={{ fontSize: '24px', cursor: 'pointer' }} />
              <GithubOutlined style={{ fontSize: '24px', cursor: 'pointer' }} />
            </Space>
          </FooterSection>
        </Col>
      </Row>
      
      <Copyright>
        <Text>© 2024 收书卖书平台. All rights reserved. | 京ICP备12345678号</Text>
      </Copyright>
    </StyledFooter>
  );
};

export default Footer;
