import React, { useEffect, useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  DatePicker,
  Typography,
  Avatar,
  Popconfirm,
  message
} from 'antd';
import {
  UserOutlined,
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { User } from '../../types';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const AdminUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      // 这里应该调用获取用户列表的API
      // const response = await adminService.getUsers(params);
      // if (response.success) {
      //   setUsers(response.data.users);
      //   setPagination(response.data.pagination);
      // }
      
      // 模拟数据
      setTimeout(() => {
        const mockUsers: User[] = [
          {
            id: '1',
            username: 'testuser1',
            phone: '13800000001',
            email: '<EMAIL>',
            role: 'user',
            status: 'active',
            register_type: 'phone',
            phone_verified: true,
            email_verified: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
          },
          {
            id: '2',
            username: 'admin',
            phone: '13800000002',
            email: '<EMAIL>',
            role: 'admin',
            status: 'active',
            register_type: 'phone',
            phone_verified: true,
            email_verified: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z'
          }
        ];
        setUsers(mockUsers);
        setPagination({
          current: 1,
          pageSize: 20,
          total: mockUsers.length
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('加载用户列表失败:', error);
      setLoading(false);
    }
  };

  const handleStatusChange = async (userId: string, status: string) => {
    try {
      // 这里应该调用更新用户状态的API
      // const response = await adminService.updateUserStatus(userId, status);
      // if (response.success) {
      //   message.success('用户状态更新成功');
      //   loadUsers();
      // }
      
      message.success('用户状态更新成功');
      loadUsers();
    } catch (error) {
      console.error('更新用户状态失败:', error);
      message.error('更新用户状态失败');
    }
  };

  const getRoleTag = (role: string) => {
    const roleMap: Record<string, { color: string; text: string }> = {
      user: { color: 'default', text: '普通用户' },
      admin: { color: 'blue', text: '管理员' },
      super_admin: { color: 'red', text: '超级管理员' }
    };
    const config = roleMap[role] || { color: 'default', text: role };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      active: { color: 'green', text: '正常' },
      inactive: { color: 'orange', text: '未激活' },
      banned: { color: 'red', text: '已封禁' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns: ColumnsType<User> = [
    {
      title: '用户信息',
      key: 'user',
      render: (_, record) => (
        <Space>
          <Avatar src={record.avatar} icon={<UserOutlined />} />
          <div>
            <div>{record.username || record.phone}</div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              {record.phone}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (email) => email || '-'
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => getRoleTag(role)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: '注册时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => message.info('编辑功能开发中')}
          >
            编辑
          </Button>
          {record.status === 'active' ? (
            <Popconfirm
              title="确定要封禁这个用户吗？"
              onConfirm={() => handleStatusChange(record.id, 'banned')}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" danger>
                封禁
              </Button>
            </Popconfirm>
          ) : (
            <Button
              type="link"
              size="small"
              onClick={() => handleStatusChange(record.id, 'active')}
            >
              解封
            </Button>
          )}
        </Space>
      )
    }
  ];

  return (
    <div>
      <Title level={2}>用户管理</Title>
      
      <Card>
        {/* 搜索和筛选 */}
        <Space style={{ marginBottom: 16 }} wrap>
          <Search
            placeholder="搜索用户名、手机号、邮箱"
            allowClear
            style={{ width: 300 }}
            onSearch={(value) => console.log('搜索:', value)}
          />
          <Select
            placeholder="用户角色"
            allowClear
            style={{ width: 120 }}
            onChange={(value) => console.log('角色筛选:', value)}
          >
            <Option value="user">普通用户</Option>
            <Option value="admin">管理员</Option>
            <Option value="super_admin">超级管理员</Option>
          </Select>
          <Select
            placeholder="用户状态"
            allowClear
            style={{ width: 120 }}
            onChange={(value) => console.log('状态筛选:', value)}
          >
            <Option value="active">正常</Option>
            <Option value="inactive">未激活</Option>
            <Option value="banned">已封禁</Option>
          </Select>
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={(dates) => console.log('日期筛选:', dates)}
          />
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => message.info('添加用户功能开发中')}
          >
            添加用户
          </Button>
        </Space>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination({ ...pagination, current: page, pageSize: pageSize || 20 });
              loadUsers();
            }
          }}
        />
      </Card>
    </div>
  );
};

export default AdminUsers;
