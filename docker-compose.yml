version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: booktrading-postgres
    environment:
      POSTGRES_DB: booktrading
      POSTGRES_USER: booktrading
      POSTGRES_PASSWORD: booktrading123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/database:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - booktrading-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U booktrading -d booktrading"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: booktrading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - booktrading-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: booktrading-backend
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: booktrading
      DB_USER: booktrading
      DB_PASSWORD: booktrading123
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 7d
      UPLOAD_PATH: /app/uploads
    volumes:
      - uploads_data:/app/uploads
      - ./backend/logs:/app/logs
    ports:
      - "3001:3001"
    networks:
      - booktrading-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        REACT_APP_API_URL: http://localhost:3001/api
    container_name: booktrading-frontend
    ports:
      - "3000:80"
    networks:
      - booktrading-network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理 (可选，用于生产环境)
  nginx:
    image: nginx:alpine
    container_name: booktrading-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - uploads_data:/var/www/uploads:ro
    networks:
      - booktrading-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local

networks:
  booktrading-network:
    driver: bridge
