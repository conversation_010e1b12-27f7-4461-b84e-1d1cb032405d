import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Input,
  Badge,
  Dropdown,
  Avatar,
  Button,
  Space,
  MenuProps
} from 'antd';
import {
  HomeOutlined,
  BookOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  SearchOutlined,
  LoginOutlined,
  LogoutOutlined,
  SettingOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';
import { useCartStore } from '../../stores/cartStore';

const { Header: AntdHeader } = Layout;
const { Search } = Input;

const StyledHeader = styled(AntdHeader)`
  display: flex;
  align-items: center;
  padding: 0 24px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
`;

const Logo = styled.div`
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
  margin-right: 32px;
  white-space: nowrap;
`;

const NavMenu = styled(Menu)`
  flex: 1;
  border: none;
  
  .ant-menu-item {
    margin: 0 8px;
  }
`;

const SearchContainer = styled.div`
  margin: 0 24px;
  
  .ant-input-search {
    width: 300px;
  }
`;

const UserActions = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const Header: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user, logout } = useAuthStore();
  const { totalItems } = useCartStore();
  const [searchValue, setSearchValue] = useState('');

  // 获取当前选中的菜单项
  const getSelectedKey = () => {
    const path = location.pathname;
    if (path === '/') return 'home';
    if (path.startsWith('/books')) return 'books';
    if (path.startsWith('/cart')) return 'cart';
    if (path.startsWith('/orders')) return 'orders';
    if (path.startsWith('/profile')) return 'profile';
    if (path.startsWith('/admin')) return 'admin';
    return '';
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    if (value.trim()) {
      navigate(`/books?search=${encodeURIComponent(value.trim())}`);
    }
  };

  // 用户菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => navigate('/profile')
    },
    {
      key: 'orders',
      icon: <BookOutlined />,
      label: '我的订单',
      onClick: () => navigate('/orders')
    },
    ...(user?.role !== 'user' ? [{
      key: 'admin',
      icon: <DashboardOutlined />,
      label: '管理后台',
      onClick: () => navigate('/admin')
    }] : []),
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout
    }
  ];

  return (
    <StyledHeader>
      <Logo>
        <Link to="/">收书卖书</Link>
      </Logo>

      <NavMenu
        mode="horizontal"
        selectedKeys={[getSelectedKey()]}
        items={[
          {
            key: 'home',
            icon: <HomeOutlined />,
            label: <Link to="/">首页</Link>
          },
          {
            key: 'books',
            icon: <BookOutlined />,
            label: <Link to="/books">图书市场</Link>
          }
        ]}
      />

      <SearchContainer>
        <Search
          placeholder="搜索图书、作者、ISBN..."
          allowClear
          enterButton={<SearchOutlined />}
          size="middle"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onSearch={handleSearch}
        />
      </SearchContainer>

      <UserActions>
        {/* 购物车 */}
        <Badge count={totalItems} size="small">
          <Button
            type="text"
            icon={<ShoppingCartOutlined />}
            onClick={() => navigate('/cart')}
          >
            购物车
          </Button>
        </Badge>

        {/* 用户操作 */}
        {isAuthenticated ? (
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <Space style={{ cursor: 'pointer' }}>
              <Avatar
                size="small"
                src={user?.avatar}
                icon={<UserOutlined />}
              />
              <span>{user?.username || user?.phone}</span>
            </Space>
          </Dropdown>
        ) : (
          <Space>
            <Button
              type="text"
              icon={<LoginOutlined />}
              onClick={() => navigate('/login')}
            >
              登录
            </Button>
            <Button
              type="primary"
              onClick={() => navigate('/register')}
            >
              注册
            </Button>
          </Space>
        )}
      </UserActions>
    </StyledHeader>
  );
};

export default Header;
