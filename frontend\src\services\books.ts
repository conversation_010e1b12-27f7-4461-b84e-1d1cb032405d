import api from './api';
import { ApiResponse, PaginationResponse, Book, BookForm, BookSearchParams } from '../types';

export const booksService = {
  // 获取图书列表
  async getBooks(params: BookSearchParams = {}): Promise<PaginationResponse<Book>> {
    const response = await api.get('/books', { params });
    return response.data;
  },

  // 获取图书详情
  async getBook(id: string): Promise<ApiResponse<Book>> {
    const response = await api.get(`/books/${id}`);
    return response.data;
  },

  // 创建图书（管理员）
  async createBook(data: BookForm): Promise<ApiResponse<Book>> {
    const response = await api.post('/books', data);
    return response.data;
  },

  // 更新图书（管理员）
  async updateBook(id: string, data: Partial<BookForm>): Promise<ApiResponse<Book>> {
    const response = await api.put(`/books/${id}`, data);
    return response.data;
  },

  // 删除图书（管理员）
  async deleteBook(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/books/${id}`);
    return response.data;
  },

  // 获取热门图书
  async getPopularBooks(limit: number = 10): Promise<ApiResponse<Book[]>> {
    const response = await api.get('/books/featured/popular', { params: { limit } });
    return response.data;
  },

  // 获取最新图书
  async getLatestBooks(limit: number = 10): Promise<ApiResponse<Book[]>> {
    const response = await api.get('/books/featured/latest', { params: { limit } });
    return response.data;
  },

  // 批量更新图书状态（管理员）
  async batchUpdateStatus(bookIds: string[], status: string): Promise<ApiResponse> {
    const response = await api.patch('/books/batch/status', {
      book_ids: bookIds,
      status
    });
    return response.data;
  },

  // 搜索图书
  async searchBooks(query: string, params: Omit<BookSearchParams, 'search'> = {}): Promise<PaginationResponse<Book>> {
    return this.getBooks({ ...params, search: query });
  },

  // 按分类获取图书
  async getBooksByCategory(categoryId: string, params: Omit<BookSearchParams, 'category_id'> = {}): Promise<PaginationResponse<Book>> {
    return this.getBooks({ ...params, category_id: categoryId });
  },

  // 按价格范围获取图书
  async getBooksByPriceRange(minPrice: number, maxPrice: number, params: Omit<BookSearchParams, 'min_price' | 'max_price'> = {}): Promise<PaginationResponse<Book>> {
    return this.getBooks({ ...params, min_price: minPrice, max_price: maxPrice });
  },

  // 按状况获取图书
  async getBooksByCondition(condition: string, params: Omit<BookSearchParams, 'condition'> = {}): Promise<PaginationResponse<Book>> {
    return this.getBooks({ ...params, condition });
  }
};
