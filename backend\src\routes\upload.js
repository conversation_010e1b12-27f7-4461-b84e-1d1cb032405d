const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let subDir = 'others';
    
    if (file.fieldname === 'avatar') {
      subDir = 'avatars';
    } else if (file.fieldname === 'book_images') {
      subDir = 'books';
    }
    
    const fullPath = path.join(uploadDir, subDir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
    
    cb(null, fullPath);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  const allowedTypes = /jpeg|jpg|png|gif|webp/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件 (jpeg, jpg, png, gif, webp)'));
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 10 // 最多10个文件
  },
  fileFilter: fileFilter
});

// 上传头像
router.post('/avatar', upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的头像文件'
      });
    }

    const fileUrl = `/uploads/avatars/${req.file.filename}`;
    
    // 更新用户头像
    const { User } = require('../models');
    await User.update(
      { avatar: fileUrl },
      { where: { id: req.user.id } }
    );

    res.json({
      success: true,
      message: '头像上传成功',
      data: {
        url: fileUrl,
        filename: req.file.filename,
        size: req.file.size
      }
    });
  } catch (error) {
    console.error('上传头像错误:', error);
    res.status(500).json({
      success: false,
      message: '上传头像失败'
    });
  }
});

// 上传图书图片
router.post('/book-images', upload.array('book_images', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片文件'
      });
    }

    const uploadedFiles = req.files.map(file => ({
      url: `/uploads/books/${file.filename}`,
      filename: file.filename,
      size: file.size,
      originalname: file.originalname
    }));

    res.json({
      success: true,
      message: '图片上传成功',
      data: {
        files: uploadedFiles,
        count: uploadedFiles.length
      }
    });
  } catch (error) {
    console.error('上传图书图片错误:', error);
    res.status(500).json({
      success: false,
      message: '上传图片失败'
    });
  }
});

// 删除文件
router.delete('/file', async (req, res) => {
  try {
    const { filename, type } = req.body;
    
    if (!filename || !type) {
      return res.status(400).json({
        success: false,
        message: '文件名和类型是必填项'
      });
    }

    let subDir = 'others';
    if (type === 'avatar') {
      subDir = 'avatars';
    } else if (type === 'book') {
      subDir = 'books';
    }

    const filePath = path.join(uploadDir, subDir, filename);
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 删除文件
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: '文件删除成功'
    });
  } catch (error) {
    console.error('删除文件错误:', error);
    res.status(500).json({
      success: false,
      message: '删除文件失败'
    });
  }
});

// 获取文件信息
router.get('/file/:type/:filename', async (req, res) => {
  try {
    const { type, filename } = req.params;
    
    let subDir = 'others';
    if (type === 'avatars') {
      subDir = 'avatars';
    } else if (type === 'books') {
      subDir = 'books';
    }

    const filePath = path.join(uploadDir, subDir, filename);
    
    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }

    // 获取文件信息
    const stats = fs.statSync(filePath);
    
    res.json({
      success: true,
      data: {
        filename: filename,
        size: stats.size,
        created_at: stats.birthtime,
        modified_at: stats.mtime,
        url: `/uploads/${type}/${filename}`
      }
    });
  } catch (error) {
    console.error('获取文件信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取文件信息失败'
    });
  }
});

// 批量删除文件
router.delete('/batch', async (req, res) => {
  try {
    const { files } = req.body; // [{ filename, type }, ...]
    
    if (!files || !Array.isArray(files) || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的文件列表'
      });
    }

    const results = [];
    
    for (const file of files) {
      try {
        let subDir = 'others';
        if (file.type === 'avatar') {
          subDir = 'avatars';
        } else if (file.type === 'book') {
          subDir = 'books';
        }

        const filePath = path.join(uploadDir, subDir, file.filename);
        
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          results.push({
            filename: file.filename,
            success: true,
            message: '删除成功'
          });
        } else {
          results.push({
            filename: file.filename,
            success: false,
            message: '文件不存在'
          });
        }
      } catch (error) {
        results.push({
          filename: file.filename,
          success: false,
          message: error.message
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    
    res.json({
      success: true,
      message: `成功删除 ${successCount}/${files.length} 个文件`,
      data: {
        results,
        success_count: successCount,
        total_count: files.length
      }
    });
  } catch (error) {
    console.error('批量删除文件错误:', error);
    res.status(500).json({
      success: false,
      message: '批量删除文件失败'
    });
  }
});

// 错误处理中间件
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超出限制（最大5MB）'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: '文件数量超出限制（最多10个）'
      });
    }
  }
  
  res.status(400).json({
    success: false,
    message: error.message || '文件上传失败'
  });
});

module.exports = router;
