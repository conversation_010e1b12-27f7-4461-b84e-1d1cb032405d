const Notification = require('../models/Notification');
const User = require('../models/User');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

class NotificationService {
  // 创建通知
  async createNotification(data) {
    try {
      const notification = await Notification.create({
        ...data,
        sent_at: new Date()
      });
      
      logger.info(`通知创建成功: ${notification.id}`);
      return notification;
    } catch (error) {
      logger.error('创建通知失败:', error);
      throw error;
    }
  }

  // 批量创建通知
  async createBulkNotifications(notifications) {
    try {
      const result = await Notification.bulkCreate(
        notifications.map(notif => ({
          ...notif,
          sent_at: new Date()
        }))
      );
      
      logger.info(`批量创建通知成功: ${result.length} 条`);
      return result;
    } catch (error) {
      logger.error('批量创建通知失败:', error);
      throw error;
    }
  }

  // 发送系统通知
  async sendSystemNotification(userIds, title, content, options = {}) {
    const notifications = userIds.map(userId => ({
      user_id: userId,
      title,
      content,
      type: 'system',
      priority: options.priority || 'normal',
      action_type: options.action_type,
      action_data: options.action_data,
      metadata: options.metadata
    }));

    return this.createBulkNotifications(notifications);
  }

  // 发送订单通知
  async sendOrderNotification(userId, orderId, type, title, content) {
    return this.createNotification({
      user_id: userId,
      title,
      content,
      type: 'order',
      action_type: 'view_order',
      action_data: { order_id: orderId },
      priority: 'high'
    });
  }

  // 发送图书相关通知
  async sendBookNotification(userId, bookId, type, title, content) {
    return this.createNotification({
      user_id: userId,
      title,
      content,
      type: 'book',
      action_type: 'view_book',
      action_data: { book_id: bookId },
      priority: 'normal'
    });
  }

  // 发送评论通知
  async sendReviewNotification(userId, reviewId, bookId, title, content) {
    return this.createNotification({
      user_id: userId,
      title,
      content,
      type: 'review',
      action_type: 'view_review',
      action_data: { review_id: reviewId, book_id: bookId },
      priority: 'normal'
    });
  }

  // 发送全局公告
  async sendGlobalAnnouncement(title, content, options = {}) {
    try {
      // 获取所有活跃用户
      const users = await User.findAll({
        where: {
          status: 'active'
        },
        attributes: ['id']
      });

      const notifications = users.map(user => ({
        user_id: user.id,
        title,
        content,
        type: 'announcement',
        priority: options.priority || 'normal',
        is_global: true,
        expires_at: options.expires_at,
        metadata: options.metadata
      }));

      return this.createBulkNotifications(notifications);
    } catch (error) {
      logger.error('发送全局公告失败:', error);
      throw error;
    }
  }

  // 发送促销通知
  async sendPromotionNotification(userIds, title, content, promotionData) {
    const notifications = userIds.map(userId => ({
      user_id: userId,
      title,
      content,
      type: 'promotion',
      priority: 'normal',
      action_type: 'view_promotion',
      action_data: promotionData,
      expires_at: promotionData.expires_at
    }));

    return this.createBulkNotifications(notifications);
  }

  // 获取用户通知列表
  async getUserNotifications(userId, options = {}) {
    const {
      page = 1,
      limit = 20,
      type,
      status = 'unread',
      priority
    } = options;

    const where = { user_id: userId };
    
    if (type) where.type = type;
    if (status !== 'all') where.status = status;
    if (priority) where.priority = priority;

    // 排除已过期的通知
    where[Op.or] = [
      { expires_at: null },
      { expires_at: { [Op.gt]: new Date() } }
    ];

    const { count, rows } = await Notification.findAndCountAll({
      where,
      order: [
        ['priority', 'DESC'],
        ['created_at', 'DESC']
      ],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit),
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'avatar']
        }
      ]
    });

    return {
      notifications: rows,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(count / limit),
        total_items: count,
        items_per_page: parseInt(limit)
      }
    };
  }

  // 标记通知为已读
  async markAsRead(notificationIds, userId) {
    try {
      const result = await Notification.update(
        {
          status: 'read',
          read_at: new Date()
        },
        {
          where: {
            id: {
              [Op.in]: Array.isArray(notificationIds) ? notificationIds : [notificationIds]
            },
            user_id: userId
          }
        }
      );

      logger.info(`标记通知已读: 用户${userId}, 通知${notificationIds}`);
      return result;
    } catch (error) {
      logger.error('标记通知已读失败:', error);
      throw error;
    }
  }

  // 标记所有通知为已读
  async markAllAsRead(userId) {
    try {
      const result = await Notification.update(
        {
          status: 'read',
          read_at: new Date()
        },
        {
          where: {
            user_id: userId,
            status: 'unread'
          }
        }
      );

      logger.info(`标记所有通知已读: 用户${userId}`);
      return result;
    } catch (error) {
      logger.error('标记所有通知已读失败:', error);
      throw error;
    }
  }

  // 删除通知
  async deleteNotification(notificationId, userId) {
    try {
      const result = await Notification.destroy({
        where: {
          id: notificationId,
          user_id: userId
        }
      });

      logger.info(`删除通知: 用户${userId}, 通知${notificationId}`);
      return result;
    } catch (error) {
      logger.error('删除通知失败:', error);
      throw error;
    }
  }

  // 获取未读通知数量
  async getUnreadCount(userId) {
    try {
      const count = await Notification.count({
        where: {
          user_id: userId,
          status: 'unread',
          [Op.or]: [
            { expires_at: null },
            { expires_at: { [Op.gt]: new Date() } }
          ]
        }
      });

      return count;
    } catch (error) {
      logger.error('获取未读通知数量失败:', error);
      return 0;
    }
  }

  // 清理过期通知
  async cleanupExpiredNotifications() {
    try {
      const result = await Notification.destroy({
        where: {
          expires_at: {
            [Op.lt]: new Date()
          }
        }
      });

      logger.info(`清理过期通知: ${result} 条`);
      return result;
    } catch (error) {
      logger.error('清理过期通知失败:', error);
      throw error;
    }
  }

  // 获取通知统计
  async getNotificationStats(userId) {
    try {
      const stats = await Notification.findAll({
        where: { user_id: userId },
        attributes: [
          'type',
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['type', 'status'],
        raw: true
      });

      const result = {
        total: 0,
        unread: 0,
        by_type: {},
        by_status: {}
      };

      stats.forEach(stat => {
        const count = parseInt(stat.count);
        result.total += count;
        
        if (stat.status === 'unread') {
          result.unread += count;
        }

        if (!result.by_type[stat.type]) {
          result.by_type[stat.type] = 0;
        }
        result.by_type[stat.type] += count;

        if (!result.by_status[stat.status]) {
          result.by_status[stat.status] = 0;
        }
        result.by_status[stat.status] += count;
      });

      return result;
    } catch (error) {
      logger.error('获取通知统计失败:', error);
      throw error;
    }
  }
}

module.exports = new NotificationService();
