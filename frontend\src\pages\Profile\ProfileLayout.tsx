import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Row, Col, Card, Menu, Avatar, Typography, Space } from 'antd';
import {
  UserOutlined,
  ShoppingOutlined,
  SafetyOutlined,
  SettingOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useAuthStore } from '../../stores/authStore';

const { Title, Text } = Typography;

const ProfileContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
`;

const UserCard = styled(Card)`
  margin-bottom: 24px;
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .user-details {
      flex: 1;
      
      .user-name {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 4px;
      }
      
      .user-meta {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
`;

const SideMenu = styled(Menu)`
  border-radius: 8px;
  
  .ant-menu-item {
    margin: 4px 0;
    border-radius: 6px;
    
    &.ant-menu-item-selected {
      background-color: #e6f7ff;
      border-color: #1890ff;
    }
  }
`;

interface ProfileLayoutProps {
  children: React.ReactNode;
}

const ProfileLayout: React.FC<ProfileLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuthStore();

  const getSelectedKey = () => {
    const path = location.pathname;
    if (path.includes('/profile/info')) return 'info';
    if (path.includes('/profile/orders')) return 'orders';
    if (path.includes('/profile/security')) return 'security';
    return 'info';
  };

  const menuItems = [
    {
      key: 'info',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => navigate('/profile/info')
    },
    {
      key: 'orders',
      icon: <ShoppingOutlined />,
      label: '我的订单',
      onClick: () => navigate('/profile/orders')
    },
    {
      key: 'security',
      icon: <SafetyOutlined />,
      label: '账户安全',
      onClick: () => navigate('/profile/security')
    }
  ];

  return (
    <ProfileContainer>
      {/* 用户信息卡片 */}
      <UserCard>
        <div className="user-info">
          <Avatar
            size={64}
            src={user?.avatar}
            icon={<UserOutlined />}
          />
          <div className="user-details">
            <div className="user-name">
              {user?.username || user?.phone || '未设置用户名'}
            </div>
            <div className="user-meta">
              <Space split="·">
                <span>手机号: {user?.phone}</span>
                <span>注册时间: {user?.created_at ? new Date(user.created_at).toLocaleDateString() : '未知'}</span>
                {user?.role !== 'user' && (
                  <span style={{ color: '#1890ff' }}>
                    {user?.role === 'admin' ? '管理员' : '超级管理员'}
                  </span>
                )}
              </Space>
            </div>
          </div>
        </div>
      </UserCard>

      <Row gutter={[24, 24]}>
        {/* 左侧菜单 */}
        <Col xs={24} md={6}>
          <Card>
            <SideMenu
              mode="vertical"
              selectedKeys={[getSelectedKey()]}
              items={menuItems}
            />
          </Card>
        </Col>

        {/* 右侧内容 */}
        <Col xs={24} md={18}>
          {children}
        </Col>
      </Row>
    </ProfileContainer>
  );
};

export default ProfileLayout;
