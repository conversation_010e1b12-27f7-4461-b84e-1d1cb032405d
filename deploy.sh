#!/bin/bash

# 收书卖书平台部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p nginx/ssl
    mkdir -p backend/logs
    mkdir -p backend/uploads/avatars
    mkdir -p backend/uploads/books
    
    log_success "目录创建完成"
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    if [ ! -f .env ]; then
        log_info "创建 .env 文件..."
        cat > .env << EOF
# 数据库配置
POSTGRES_DB=booktrading
POSTGRES_USER=booktrading
POSTGRES_PASSWORD=booktrading123

# Redis 配置
REDIS_PASSWORD=

# 后端配置
NODE_ENV=production
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=7d

# 前端配置
REACT_APP_API_URL=http://localhost:3001/api
EOF
        log_success ".env 文件创建完成"
    else
        log_info ".env 文件已存在，跳过创建"
    fi
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动数据库和缓存
    docker-compose up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 启动后端服务
    docker-compose up -d backend
    
    # 等待后端服务启动
    log_info "等待后端服务启动..."
    sleep 10
    
    # 启动前端服务
    docker-compose up -d frontend
    
    log_success "所有服务启动完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待数据库完全启动
    sleep 5
    
    # 运行数据库初始化脚本
    docker-compose exec postgres psql -U booktrading -d booktrading -f /docker-entrypoint-initdb.d/init.sql
    
    # 插入演示数据
    if [ "$1" = "--with-demo-data" ]; then
        log_info "插入演示数据..."
        docker-compose exec postgres psql -U booktrading -d booktrading -f /docker-entrypoint-initdb.d/demo-data.sql
        log_success "演示数据插入完成"
    fi
    
    log_success "数据库初始化完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    docker-compose ps
    
    # 检查服务健康状态
    log_info "等待服务健康检查..."
    sleep 30
    
    # 检查后端服务
    if curl -f http://localhost:3001/api/health > /dev/null 2>&1; then
        log_success "后端服务运行正常"
    else
        log_warning "后端服务可能未正常启动"
    fi
    
    # 检查前端服务
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务运行正常"
    else
        log_warning "前端服务可能未正常启动"
    fi
}

# 显示访问信息
show_access_info() {
    log_success "部署完成！"
    echo
    echo "访问信息："
    echo "  前端应用: http://localhost:3000"
    echo "  后端API:  http://localhost:3001"
    echo "  数据库:   localhost:5432"
    echo "  Redis:    localhost:6379"
    echo
    echo "默认账户："
    echo "  超级管理员: 13800000000 / admin123456"
    echo "  普通管理员: 13800000001 / admin123456"
    echo "  测试用户:   13800000002-13800000006 / test123456"
    echo
    echo "管理命令："
    echo "  查看日志: docker-compose logs -f [service_name]"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart [service_name]"
    echo
}

# 清理函数
cleanup() {
    log_info "停止所有服务..."
    docker-compose down
    
    if [ "$1" = "--remove-volumes" ]; then
        log_warning "删除所有数据卷..."
        docker-compose down -v
        docker volume prune -f
    fi
    
    if [ "$1" = "--remove-images" ]; then
        log_warning "删除构建的镜像..."
        docker-compose down --rmi all
    fi
    
    log_success "清理完成"
}

# 主函数
main() {
    case "$1" in
        "deploy")
            check_dependencies
            create_directories
            setup_environment
            build_images
            start_services
            init_database "$2"
            check_services
            show_access_info
            ;;
        "start")
            log_info "启动现有服务..."
            docker-compose up -d
            check_services
            show_access_info
            ;;
        "stop")
            log_info "停止所有服务..."
            docker-compose down
            log_success "服务已停止"
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose restart
            check_services
            ;;
        "logs")
            docker-compose logs -f "${2:-}"
            ;;
        "cleanup")
            cleanup "$2"
            ;;
        "status")
            docker-compose ps
            ;;
        *)
            echo "用法: $0 {deploy|start|stop|restart|logs|cleanup|status}"
            echo
            echo "命令说明："
            echo "  deploy [--with-demo-data]  完整部署（首次使用）"
            echo "  start                      启动现有服务"
            echo "  stop                       停止所有服务"
            echo "  restart                    重启服务"
            echo "  logs [service_name]        查看日志"
            echo "  cleanup [--remove-volumes|--remove-images]  清理"
            echo "  status                     查看服务状态"
            echo
            echo "示例："
            echo "  $0 deploy --with-demo-data  # 部署并插入演示数据"
            echo "  $0 logs backend             # 查看后端日志"
            echo "  $0 cleanup --remove-volumes # 清理并删除数据"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
