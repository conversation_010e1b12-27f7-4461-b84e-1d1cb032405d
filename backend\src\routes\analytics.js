const express = require('express');
const { Op, fn, col, literal } = require('sequelize');
const { Book, User, Order, OrderItem, Review, Favorite, Category } = require('../models');
const { authenticateToken, requireRole } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// 获取平台总体统计数据
router.get('/overview', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    // 计算时间范围
    const now = new Date();
    let startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // 并行获取各项统计数据
    const [
      totalUsers,
      totalBooks,
      totalOrders,
      totalRevenue,
      newUsers,
      newBooks,
      newOrders,
      periodRevenue,
      topCategories,
      recentActivity
    ] = await Promise.all([
      // 总用户数
      User.count(),
      
      // 总图书数
      Book.count({ where: { status: '上架' } }),
      
      // 总订单数
      Order.count({ where: { status: { [Op.in]: ['paid', 'delivering', 'delivered'] } } }),
      
      // 总收入
      Order.sum('total_amount', { 
        where: { status: { [Op.in]: ['paid', 'delivering', 'delivered'] } } 
      }),
      
      // 新增用户
      User.count({
        where: {
          created_at: { [Op.gte]: startDate }
        }
      }),
      
      // 新增图书
      Book.count({
        where: {
          created_at: { [Op.gte]: startDate },
          status: '上架'
        }
      }),
      
      // 新增订单
      Order.count({
        where: {
          created_at: { [Op.gte]: startDate },
          status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
        }
      }),
      
      // 期间收入
      Order.sum('total_amount', {
        where: {
          created_at: { [Op.gte]: startDate },
          status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
        }
      }),
      
      // 热门分类
      Category.findAll({
        include: [{
          model: Book,
          as: 'books',
          attributes: [],
          where: { status: '上架' }
        }],
        attributes: [
          'id',
          'name',
          [fn('COUNT', col('books.id')), 'book_count']
        ],
        group: ['Category.id', 'Category.name'],
        order: [[literal('book_count'), 'DESC']],
        limit: 5
      }),
      
      // 最近活动
      Order.findAll({
        include: [{
          model: User,
          as: 'user',
          attributes: ['username', 'phone']
        }],
        order: [['created_at', 'DESC']],
        limit: 10,
        attributes: ['id', 'order_number', 'total_amount', 'status', 'created_at']
      })
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          total_users: totalUsers || 0,
          total_books: totalBooks || 0,
          total_orders: totalOrders || 0,
          total_revenue: totalRevenue || 0,
          new_users: newUsers || 0,
          new_books: newBooks || 0,
          new_orders: newOrders || 0,
          period_revenue: periodRevenue || 0
        },
        top_categories: topCategories,
        recent_activity: recentActivity,
        period
      }
    });
  } catch (error) {
    logger.error('获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败'
    });
  }
});

// 获取销售趋势数据
router.get('/sales-trend', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { period = '30d', granularity = 'day' } = req.query;
    
    // 计算时间范围
    const now = new Date();
    let startDate = new Date();
    let dateFormat = '%Y-%m-%d';
    
    switch (period) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        dateFormat = '%Y-%m-%d';
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        dateFormat = granularity === 'week' ? '%Y-%u' : '%Y-%m-%d';
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        dateFormat = '%Y-%u';
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        dateFormat = '%Y-%m';
        break;
    }

    const salesData = await Order.findAll({
      where: {
        created_at: { [Op.gte]: startDate },
        status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
      },
      attributes: [
        [fn('DATE_FORMAT', col('created_at'), dateFormat), 'date'],
        [fn('COUNT', col('id')), 'order_count'],
        [fn('SUM', col('total_amount')), 'revenue']
      ],
      group: [fn('DATE_FORMAT', col('created_at'), dateFormat)],
      order: [[fn('DATE_FORMAT', col('created_at'), dateFormat), 'ASC']],
      raw: true
    });

    res.json({
      success: true,
      data: {
        sales_trend: salesData,
        period,
        granularity
      }
    });
  } catch (error) {
    logger.error('获取销售趋势失败:', error);
    res.status(500).json({
      success: false,
      message: '获取销售趋势失败'
    });
  }
});

// 获取用户行为分析
router.get('/user-behavior', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    const now = new Date();
    let startDate = new Date();
    startDate.setDate(now.getDate() - parseInt(period.replace('d', '')));

    const [
      userRegistrations,
      userActivity,
      topUsers,
      userRetention
    ] = await Promise.all([
      // 用户注册趋势
      User.findAll({
        where: {
          created_at: { [Op.gte]: startDate }
        },
        attributes: [
          [fn('DATE', col('created_at')), 'date'],
          [fn('COUNT', col('id')), 'count']
        ],
        group: [fn('DATE', col('created_at'))],
        order: [[fn('DATE', col('created_at')), 'ASC']],
        raw: true
      }),
      
      // 用户活跃度
      Order.findAll({
        where: {
          created_at: { [Op.gte]: startDate }
        },
        include: [{
          model: User,
          as: 'user',
          attributes: ['id']
        }],
        attributes: [
          [fn('DATE', col('Order.created_at')), 'date'],
          [fn('COUNT', fn('DISTINCT', col('user.id'))), 'active_users']
        ],
        group: [fn('DATE', col('Order.created_at'))],
        order: [[fn('DATE', col('Order.created_at')), 'ASC']],
        raw: true
      }),
      
      // 活跃用户排行
      User.findAll({
        include: [{
          model: Order,
          as: 'orders',
          where: {
            created_at: { [Op.gte]: startDate },
            status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
          },
          attributes: []
        }],
        attributes: [
          'id',
          'username',
          'phone',
          [fn('COUNT', col('orders.id')), 'order_count'],
          [fn('SUM', col('orders.total_amount')), 'total_spent']
        ],
        group: ['User.id'],
        order: [[literal('order_count'), 'DESC']],
        limit: 10
      }),
      
      // 用户留存率（简化计算）
      User.findAll({
        where: {
          created_at: { [Op.gte]: startDate }
        },
        include: [{
          model: Order,
          as: 'orders',
          required: false,
          where: {
            created_at: { [Op.gte]: startDate }
          }
        }],
        attributes: [
          [fn('DATE', col('User.created_at')), 'registration_date'],
          [fn('COUNT', col('User.id')), 'registered_users'],
          [fn('COUNT', col('orders.id')), 'active_users']
        ],
        group: [fn('DATE', col('User.created_at'))],
        order: [[fn('DATE', col('User.created_at')), 'ASC']],
        raw: true
      })
    ]);

    res.json({
      success: true,
      data: {
        user_registrations: userRegistrations,
        user_activity: userActivity,
        top_users: topUsers,
        user_retention: userRetention,
        period
      }
    });
  } catch (error) {
    logger.error('获取用户行为分析失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户行为分析失败'
    });
  }
});

// 获取图书分析数据
router.get('/books-analysis', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const [
      topSellingBooks,
      categoryDistribution,
      priceDistribution,
      conditionStats,
      viewsStats
    ] = await Promise.all([
      // 热销图书
      Book.findAll({
        include: [{
          model: OrderItem,
          as: 'orderItems',
          include: [{
            model: Order,
            as: 'order',
            where: { status: { [Op.in]: ['paid', 'delivering', 'delivered'] } }
          }]
        }],
        attributes: [
          'id',
          'title',
          'author',
          'price',
          'cover_image',
          [fn('SUM', col('orderItems.quantity')), 'total_sold']
        ],
        group: ['Book.id'],
        order: [[literal('total_sold'), 'DESC']],
        limit: 10
      }),
      
      // 分类分布
      Category.findAll({
        include: [{
          model: Book,
          as: 'books',
          where: { status: '上架' },
          attributes: []
        }],
        attributes: [
          'id',
          'name',
          [fn('COUNT', col('books.id')), 'book_count']
        ],
        group: ['Category.id'],
        order: [[literal('book_count'), 'DESC']]
      }),
      
      // 价格分布
      Book.findAll({
        where: { status: '上架' },
        attributes: [
          [literal('CASE WHEN price < 20 THEN "0-20" WHEN price < 50 THEN "20-50" WHEN price < 100 THEN "50-100" ELSE "100+" END'), 'price_range'],
          [fn('COUNT', col('id')), 'count']
        ],
        group: [literal('CASE WHEN price < 20 THEN "0-20" WHEN price < 50 THEN "20-50" WHEN price < 100 THEN "50-100" ELSE "100+" END')],
        raw: true
      }),
      
      // 图书状况统计
      Book.findAll({
        where: { status: '上架' },
        attributes: [
          'condition',
          [fn('COUNT', col('id')), 'count']
        ],
        group: ['condition'],
        raw: true
      }),
      
      // 浏览量统计
      Book.findAll({
        where: { status: '上架' },
        attributes: [
          'id',
          'title',
          'views',
          'sales_count'
        ],
        order: [['views', 'DESC']],
        limit: 10
      })
    ]);

    res.json({
      success: true,
      data: {
        top_selling_books: topSellingBooks,
        category_distribution: categoryDistribution,
        price_distribution: priceDistribution,
        condition_stats: conditionStats,
        most_viewed_books: viewsStats
      }
    });
  } catch (error) {
    logger.error('获取图书分析数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取图书分析数据失败'
    });
  }
});

// 获取收入分析
router.get('/revenue-analysis', authenticateToken, requireRole(['admin', 'super_admin']), async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    const now = new Date();
    let startDate = new Date();
    startDate.setDate(now.getDate() - parseInt(period.replace('d', '')));

    const [
      revenueByCategory,
      revenueByPaymentMethod,
      monthlyRevenue,
      averageOrderValue
    ] = await Promise.all([
      // 按分类收入
      OrderItem.findAll({
        include: [
          {
            model: Order,
            as: 'order',
            where: {
              created_at: { [Op.gte]: startDate },
              status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
            },
            attributes: []
          },
          {
            model: Book,
            as: 'book',
            include: [{
              model: Category,
              as: 'category',
              attributes: ['name']
            }],
            attributes: []
          }
        ],
        attributes: [
          [col('book.category.name'), 'category_name'],
          [fn('SUM', literal('quantity * price')), 'revenue']
        ],
        group: [col('book.category.name')],
        order: [[literal('revenue'), 'DESC']],
        raw: true
      }),
      
      // 按支付方式收入
      Order.findAll({
        where: {
          created_at: { [Op.gte]: startDate },
          status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
        },
        attributes: [
          'payment_method',
          [fn('COUNT', col('id')), 'order_count'],
          [fn('SUM', col('total_amount')), 'revenue']
        ],
        group: ['payment_method'],
        raw: true
      }),
      
      // 月度收入趋势
      Order.findAll({
        where: {
          created_at: { [Op.gte]: startDate },
          status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
        },
        attributes: [
          [fn('DATE_FORMAT', col('created_at'), '%Y-%m'), 'month'],
          [fn('SUM', col('total_amount')), 'revenue'],
          [fn('COUNT', col('id')), 'order_count']
        ],
        group: [fn('DATE_FORMAT', col('created_at'), '%Y-%m')],
        order: [[fn('DATE_FORMAT', col('created_at'), '%Y-%m'), 'ASC']],
        raw: true
      }),
      
      // 平均订单价值
      Order.findOne({
        where: {
          created_at: { [Op.gte]: startDate },
          status: { [Op.in]: ['paid', 'delivering', 'delivered'] }
        },
        attributes: [
          [fn('AVG', col('total_amount')), 'average_order_value'],
          [fn('COUNT', col('id')), 'total_orders'],
          [fn('SUM', col('total_amount')), 'total_revenue']
        ],
        raw: true
      })
    ]);

    res.json({
      success: true,
      data: {
        revenue_by_category: revenueByCategory,
        revenue_by_payment_method: revenueByPaymentMethod,
        monthly_revenue: monthlyRevenue,
        average_order_value: averageOrderValue,
        period
      }
    });
  } catch (error) {
    logger.error('获取收入分析失败:', error);
    res.status(500).json({
      success: false,
      message: '获取收入分析失败'
    });
  }
});

module.exports = router;
