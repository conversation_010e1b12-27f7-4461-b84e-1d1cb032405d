import React, { useState, useEffect } from 'react';
import {
  Card,
  Radio,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Input,
  Form,
  Modal,
  QRCode,
  Spin,
  message,
  Alert,
  Steps,
  Result
} from 'antd';
import {
  AlipayOutlined,
  WechatOutlined,
  CreditCardOutlined,
  BankOutlined,
  SafetyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;
const { Step } = Steps;

const PaymentContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  
  .payment-header {
    text-align: center;
    margin-bottom: 32px;
    
    .header-title {
      margin-bottom: 8px;
    }
    
    .header-subtitle {
      color: #8c8c8c;
    }
  }
  
  .payment-steps {
    margin-bottom: 32px;
    
    .ant-steps-item-title {
      font-size: 14px;
    }
  }
  
  .payment-content {
    .order-summary {
      background: #fafafa;
      padding: 20px;
      border-radius: 12px;
      margin-bottom: 24px;
      
      .summary-header {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 16px;
      }
      
      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        &.total {
          font-size: 18px;
          font-weight: 700;
          color: #ff4d4f;
          border-top: 1px solid #f0f0f0;
          padding-top: 12px;
          margin-top: 12px;
        }
      }
    }
    
    .payment-methods {
      .method-option {
        padding: 16px;
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: #1677ff;
        }
        
        &.selected {
          border-color: #1677ff;
          background: #f6f9ff;
        }
        
        .method-content {
          display: flex;
          align-items: center;
          gap: 12px;
          
          .method-icon {
            font-size: 24px;
            
            &.alipay {
              color: #1677ff;
            }
            
            &.wechat {
              color: #07c160;
            }
            
            &.card {
              color: #722ed1;
            }
            
            &.bank {
              color: #fa8c16;
            }
          }
          
          .method-info {
            flex: 1;
            
            .method-name {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 4px;
            }
            
            .method-desc {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
          
          .method-badge {
            font-size: 11px;
            padding: 2px 6px;
            background: #f6ffed;
            color: #52c41a;
            border-radius: 4px;
          }
        }
      }
    }
    
    .payment-form {
      margin-top: 24px;
      
      .form-section {
        margin-bottom: 24px;
        
        .section-title {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 12px;
        }
      }
    }
    
    .payment-actions {
      margin-top: 32px;
      text-align: center;
      
      .pay-button {
        height: 48px;
        padding: 0 48px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        background: linear-gradient(135deg, #1677ff, #4096ff);
        border: none;
        
        &:hover {
          background: linear-gradient(135deg, #0958d9, #1677ff);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
        }
      }
      
      .security-info {
        margin-top: 16px;
        color: #8c8c8c;
        font-size: 12px;
        
        .security-icon {
          margin-right: 4px;
          color: #52c41a;
        }
      }
    }
  }
  
  .qr-payment {
    text-align: center;
    padding: 32px;
    
    .qr-title {
      margin-bottom: 16px;
    }
    
    .qr-code {
      margin-bottom: 16px;
    }
    
    .qr-tips {
      color: #8c8c8c;
      font-size: 14px;
      margin-bottom: 24px;
    }
    
    .payment-status {
      .status-icon {
        font-size: 48px;
        margin-bottom: 16px;
        
        &.waiting {
          color: #1677ff;
        }
        
        &.success {
          color: #52c41a;
        }
        
        &.failed {
          color: #ff4d4f;
        }
      }
      
      .status-text {
        font-size: 16px;
        margin-bottom: 8px;
      }
      
      .status-desc {
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }
`;

interface PaymentSystemProps {
  orderData: {
    id: string;
    total_amount: number;
    items: any[];
    shipping_fee?: number;
    discount?: number;
  };
  onPaymentSuccess: (paymentResult: any) => void;
  onPaymentCancel: () => void;
  className?: string;
}

const PaymentSystem: React.FC<PaymentSystemProps> = ({
  orderData,
  onPaymentSuccess,
  onPaymentCancel,
  className
}) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedMethod, setSelectedMethod] = useState('alipay');
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [qrCodeVisible, setQrCodeVisible] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'waiting' | 'success' | 'failed' | null>(null);
  const [form] = Form.useForm();

  const paymentMethods = [
    {
      key: 'alipay',
      name: '支付宝',
      description: '推荐使用，安全快捷',
      icon: <AlipayOutlined className="method-icon alipay" />,
      badge: '推荐',
      type: 'qr'
    },
    {
      key: 'wechat',
      name: '微信支付',
      description: '微信扫码支付',
      icon: <WechatOutlined className="method-icon wechat" />,
      badge: null,
      type: 'qr'
    },
    {
      key: 'card',
      name: '银行卡支付',
      description: '支持各大银行储蓄卡和信用卡',
      icon: <CreditCardOutlined className="method-icon card" />,
      badge: null,
      type: 'form'
    },
    {
      key: 'bank',
      name: '网银支付',
      description: '跳转到银行官网支付',
      icon: <BankOutlined className="method-icon bank" />,
      badge: null,
      type: 'redirect'
    }
  ];

  const steps = [
    {
      title: '确认订单',
      description: '核对订单信息'
    },
    {
      title: '选择支付方式',
      description: '选择合适的支付方式'
    },
    {
      title: '完成支付',
      description: '支付成功'
    }
  ];

  useEffect(() => {
    // 模拟支付状态检查
    let statusTimer: NodeJS.Timeout;
    
    if (paymentStatus === 'waiting') {
      statusTimer = setTimeout(() => {
        // 模拟支付成功
        setPaymentStatus('success');
        setCurrentStep(2);
        setTimeout(() => {
          onPaymentSuccess({
            payment_id: 'PAY' + Date.now(),
            method: selectedMethod,
            amount: orderData.total_amount
          });
        }, 2000);
      }, 5000);
    }

    return () => {
      if (statusTimer) clearTimeout(statusTimer);
    };
  }, [paymentStatus, selectedMethod, orderData.total_amount, onPaymentSuccess]);

  const handlePayment = async () => {
    const selectedMethodData = paymentMethods.find(m => m.key === selectedMethod);
    
    if (!selectedMethodData) return;

    setPaymentLoading(true);
    setCurrentStep(1);

    try {
      // 模拟支付请求
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (selectedMethodData.type === 'qr') {
        // 二维码支付
        setQrCodeVisible(true);
        setPaymentStatus('waiting');
      } else if (selectedMethodData.type === 'form') {
        // 表单支付
        const values = await form.validateFields();
        // 处理银行卡支付
        setPaymentStatus('success');
        setCurrentStep(2);
        setTimeout(() => {
          onPaymentSuccess({
            payment_id: 'PAY' + Date.now(),
            method: selectedMethod,
            amount: orderData.total_amount
          });
        }, 1000);
      } else if (selectedMethodData.type === 'redirect') {
        // 跳转支付
        message.info('正在跳转到银行支付页面...');
        // 实际项目中这里会跳转到银行页面
      }
    } catch (error) {
      message.error('支付失败，请重试');
      setPaymentStatus('failed');
    } finally {
      setPaymentLoading(false);
    }
  };

  const renderPaymentContent = () => {
    if (qrCodeVisible) {
      return (
        <div className="qr-payment">
          <Title level={4} className="qr-title">
            请使用{paymentMethods.find(m => m.key === selectedMethod)?.name}扫码支付
          </Title>
          
          <div className="qr-code">
            <QRCode
              value={`payment:${orderData.id}:${selectedMethod}:${orderData.total_amount}`}
              size={200}
            />
          </div>
          
          <div className="qr-tips">
            请使用手机{selectedMethod === 'alipay' ? '支付宝' : '微信'}扫描上方二维码完成支付
          </div>
          
          <div className="payment-status">
            {paymentStatus === 'waiting' && (
              <>
                <LoadingOutlined className="status-icon waiting" />
                <div className="status-text">等待支付中...</div>
                <div className="status-desc">请在手机上确认支付</div>
              </>
            )}
            
            {paymentStatus === 'success' && (
              <>
                <CheckCircleOutlined className="status-icon success" />
                <div className="status-text">支付成功！</div>
                <div className="status-desc">正在跳转到订单详情页...</div>
              </>
            )}
            
            {paymentStatus === 'failed' && (
              <>
                <ClockCircleOutlined className="status-icon failed" />
                <div className="status-text">支付失败</div>
                <div className="status-desc">请重新尝试支付</div>
              </>
            )}
          </div>
          
          <Space style={{ marginTop: 24 }}>
            <Button onClick={() => {
              setQrCodeVisible(false);
              setPaymentStatus(null);
              setCurrentStep(0);
            }}>
              重新选择
            </Button>
            <Button type="primary" onClick={onPaymentCancel}>
              取消支付
            </Button>
          </Space>
        </div>
      );
    }

    if (currentStep === 2 && paymentStatus === 'success') {
      return (
        <Result
          status="success"
          title="支付成功！"
          subTitle={`订单号: ${orderData.id} | 支付金额: ¥${orderData.total_amount.toFixed(2)}`}
          extra={[
            <Button type="primary" key="order" onClick={() => navigate(`/orders/${orderData.id}`)}>
              查看订单
            </Button>,
            <Button key="continue" onClick={() => navigate('/books')}>
              继续购物
            </Button>
          ]}
        />
      );
    }

    return (
      <div className="payment-content">
        {/* 订单摘要 */}
        <div className="order-summary">
          <div className="summary-header">订单摘要</div>
          <div className="summary-item">
            <span>商品金额</span>
            <span>¥{(orderData.total_amount - (orderData.shipping_fee || 0) + (orderData.discount || 0)).toFixed(2)}</span>
          </div>
          {orderData.shipping_fee && orderData.shipping_fee > 0 && (
            <div className="summary-item">
              <span>运费</span>
              <span>¥{orderData.shipping_fee.toFixed(2)}</span>
            </div>
          )}
          {orderData.discount && orderData.discount > 0 && (
            <div className="summary-item">
              <span>优惠金额</span>
              <span style={{ color: '#52c41a' }}>-¥{orderData.discount.toFixed(2)}</span>
            </div>
          )}
          <div className="summary-item total">
            <span>应付金额</span>
            <span>¥{orderData.total_amount.toFixed(2)}</span>
          </div>
        </div>

        {/* 支付方式选择 */}
        <Card title="选择支付方式" bordered={false}>
          <div className="payment-methods">
            {paymentMethods.map(method => (
              <div
                key={method.key}
                className={`method-option ${selectedMethod === method.key ? 'selected' : ''}`}
                onClick={() => setSelectedMethod(method.key)}
              >
                <div className="method-content">
                  <Radio checked={selectedMethod === method.key} />
                  {method.icon}
                  <div className="method-info">
                    <div className="method-name">{method.name}</div>
                    <div className="method-desc">{method.description}</div>
                  </div>
                  {method.badge && (
                    <div className="method-badge">{method.badge}</div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* 银行卡支付表单 */}
          {selectedMethod === 'card' && (
            <div className="payment-form">
              <Form form={form} layout="vertical">
                <div className="form-section">
                  <div className="section-title">银行卡信息</div>
                  <Row gutter={16}>
                    <Col span={24}>
                      <Form.Item
                        name="cardNumber"
                        label="银行卡号"
                        rules={[{ required: true, message: '请输入银行卡号' }]}
                      >
                        <Input placeholder="请输入银行卡号" maxLength={19} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="expiryDate"
                        label="有效期"
                        rules={[{ required: true, message: '请输入有效期' }]}
                      >
                        <Input placeholder="MM/YY" maxLength={5} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="cvv"
                        label="CVV"
                        rules={[{ required: true, message: '请输入CVV' }]}
                      >
                        <Input placeholder="CVV" maxLength={3} />
                      </Form.Item>
                    </Col>
                    <Col span={24}>
                      <Form.Item
                        name="cardHolder"
                        label="持卡人姓名"
                        rules={[{ required: true, message: '请输入持卡人姓名' }]}
                      >
                        <Input placeholder="请输入持卡人姓名" />
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </Form>
            </div>
          )}
        </Card>

        {/* 支付按钮 */}
        <div className="payment-actions">
          <Button
            type="primary"
            className="pay-button"
            loading={paymentLoading}
            onClick={handlePayment}
          >
            确认支付 ¥{orderData.total_amount.toFixed(2)}
          </Button>
          
          <div className="security-info">
            <SafetyOutlined className="security-icon" />
            您的支付信息将通过SSL加密传输，确保安全
          </div>
        </div>
      </div>
    );
  };

  return (
    <PaymentContainer className={className}>
      <div className="payment-header">
        <Title level={2} className="header-title">安全支付</Title>
        <Text className="header-subtitle">请选择支付方式完成订单</Text>
      </div>

      <Steps current={currentStep} className="payment-steps">
        {steps.map(step => (
          <Step key={step.title} title={step.title} description={step.description} />
        ))}
      </Steps>

      {renderPaymentContent()}
    </PaymentContainer>
  );
};

export default PaymentSystem;
