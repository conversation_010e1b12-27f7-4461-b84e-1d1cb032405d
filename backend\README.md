# 收书卖书平台 - 后端API

大学生二手书交易平台的后端服务，基于 Node.js + Express + PostgreSQL 构建。

## 功能特性

### 核心功能
- 🔐 用户认证与授权（JWT）
- 📚 图书管理（CRUD、搜索、分类）
- 🛒 订单系统（创建、支付、配送、退货）
- 💬 实时消息系统（Socket.IO）
- 📁 文件上传（图片）
- 👥 用户管理
- 📊 管理员后台

### 技术特性
- RESTful API 设计
- 数据验证与错误处理
- 速率限制
- 文件上传处理
- 实时通信
- 数据库事务
- 安全中间件

## 技术栈

- **运行时**: Node.js 18+
- **框架**: Express.js
- **数据库**: PostgreSQL + Sequelize ORM
- **认证**: JWT + bcryptjs
- **实时通信**: Socket.IO
- **文件上传**: Multer
- **数据验证**: Joi
- **安全**: Helmet, CORS, Rate Limiting

## 快速开始

### 环境要求

- Node.js 18.0+
- PostgreSQL 12+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 环境配置

1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置数据库连接等信息：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=book_trading
DB_USER=postgres
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3001
NODE_ENV=development
```

### 数据库初始化

1. 创建数据库：
```sql
CREATE DATABASE book_trading;
```

2. 运行数据库迁移和种子数据：
```bash
npm run seed
```

### 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

服务将在 `http://localhost:3001` 启动。

## API 文档

### 认证相关

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "phone": "13800000000",
  "password": "123456",
  "username": "testuser",
  "email": "<EMAIL>"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "phone": "13800000000",
  "password": "123456"
}
```

### 图书相关

#### 获取图书列表
```http
GET /api/books?page=1&limit=20&search=JavaScript&category_id=uuid
```

#### 获取图书详情
```http
GET /api/books/:id
```

#### 创建图书（管理员）
```http
POST /api/books
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "JavaScript高级程序设计",
  "author": "Matt Frisbie",
  "publisher": "人民邮电出版社",
  "isbn": "9787115545381",
  "category_id": "uuid",
  "description": "JavaScript权威指南",
  "condition": "全新",
  "price": 89.00,
  "original_price": 129.00,
  "stock": 10
}
```

### 订单相关

#### 创建订单
```http
POST /api/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "items": [
    {
      "book_id": "uuid",
      "quantity": 2
    }
  ],
  "delivery_address": "北京市海淀区清华大学",
  "delivery_phone": "13800000000"
}
```

#### 获取用户订单
```http
GET /api/orders?page=1&limit=20&status=pending
Authorization: Bearer <token>
```

### 文件上传

#### 上传头像
```http
POST /api/upload/avatar
Authorization: Bearer <token>
Content-Type: multipart/form-data

avatar: <file>
```

#### 上传图书图片
```http
POST /api/upload/book-images
Authorization: Bearer <token>
Content-Type: multipart/form-data

book_images: <files>
```

## 数据库模型

### 用户表 (users)
- id (UUID, 主键)
- username (字符串, 唯一)
- email (字符串, 唯一)
- phone (字符串, 唯一, 必填)
- password_hash (字符串, 必填)
- role (枚举: user, admin, super_admin)
- avatar (文本)
- status (枚举: active, inactive, banned)

### 图书表 (books)
- id (UUID, 主键)
- isbn (字符串, 唯一)
- title (字符串, 必填)
- author (字符串)
- publisher (字符串)
- category_id (UUID, 外键)
- description (文本)
- condition (枚举: 全新, 九成新, 八成新, 七成新, 六成新)
- price (数值, 必填)
- stock (整数, 必填)
- status (枚举: 上架, 下架, 缺货, 预售)

### 订单表 (orders)
- id (UUID, 主键)
- order_number (字符串, 唯一)
- user_id (UUID, 外键)
- total_amount (数值, 必填)
- status (枚举: pending, paid, delivering, delivered, cancelled)
- payment_status (枚举: pending, paid, failed, refunded)
- delivery_address (文本)

## 开发指南

### 项目结构
```
src/
├── app.js              # 应用入口
├── config/
│   └── database.js     # 数据库配置
├── models/             # 数据模型
│   ├── User.js
│   ├── Book.js
│   ├── Order.js
│   └── index.js
├── routes/             # 路由
│   ├── auth.js
│   ├── books.js
│   ├── orders.js
│   └── ...
├── middleware/         # 中间件
│   ├── auth.js
│   └── errorHandler.js
├── utils/              # 工具函数
│   └── validation.js
└── database/           # 数据库脚本
    └── seed.js
```

### 添加新功能

1. 创建数据模型（如需要）
2. 添加路由处理器
3. 实现业务逻辑
4. 添加数据验证
5. 编写测试
6. 更新API文档

### 测试

```bash
# 运行测试
npm test

# 运行测试并监听变化
npm run test:watch
```

## 部署

### Docker 部署

1. 构建镜像：
```bash
docker build -t book-trading-backend .
```

2. 运行容器：
```bash
docker run -p 3001:3001 --env-file .env book-trading-backend
```

### 生产环境配置

1. 设置环境变量
2. 配置反向代理（Nginx）
3. 设置 SSL 证书
4. 配置日志收集
5. 设置监控和告警

## 许可证

MIT License
