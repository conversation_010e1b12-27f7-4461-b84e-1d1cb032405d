const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  order_number: {
    type: DataTypes.STRING(32),
    allowNull: false,
    unique: true
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  total_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  status: {
    type: DataTypes.ENUM(
      'pending',      // 待支付
      'paid',         // 已支付
      'delivering',   // 配送中
      'delivered',    // 已送达
      'return_requested', // 退货申请
      'returned',     // 已退货
      'cancelled'     // 已取消
    ),
    defaultValue: 'pending'
  },
  payment_method: {
    type: DataTypes.ENUM('alipay', 'wechat', 'offline'),
    allowNull: true
  },
  payment_status: {
    type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded'),
    defaultValue: 'pending'
  },
  payment_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  delivery_method: {
    type: DataTypes.ENUM('platform', 'pickup'),
    defaultValue: 'platform'
  },
  delivery_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  delivery_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  delivery_person: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  delivery_person_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  delivery_time: {
    type: DataTypes.DATE,
    allowNull: true
  },
  return_reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  return_status: {
    type: DataTypes.ENUM('none', 'requested', 'approved', 'rejected', 'completed'),
    defaultValue: 'none'
  },
  return_requested_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'orders',
  hooks: {
    beforeCreate: (order) => {
      // 生成订单号：时间戳 + 随机数
      const timestamp = Date.now().toString();
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
      order.order_number = timestamp + random;
    }
  }
});

const OrderItem = sequelize.define('OrderItem', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  order_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'orders',
      key: 'id'
    }
  },
  book_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'books',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1,
    validate: {
      min: 1
    }
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  }
}, {
  tableName: 'order_items',
  hooks: {
    beforeSave: (orderItem) => {
      orderItem.subtotal = orderItem.price * orderItem.quantity;
    }
  }
});

module.exports = { Order, OrderItem };
