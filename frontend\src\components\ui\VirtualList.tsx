import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Spin } from 'antd';
import styled from 'styled-components';

const VirtualContainer = styled.div<{ height: number }>`
  height: ${props => props.height}px;
  overflow-y: auto;
  position: relative;
`;

const VirtualContent = styled.div<{ totalHeight: number }>`
  height: ${props => props.totalHeight}px;
  position: relative;
`;

const VirtualViewport = styled.div<{ translateY: number }>`
  transform: translateY(${props => props.translateY}px);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number | ((index: number, item: T) => number);
  height: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number; // 预渲染的项目数量
  onScroll?: (scrollTop: number) => void;
  onEndReached?: () => void;
  endReachedThreshold?: number; // 触发 onEndReached 的阈值
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

function VirtualList<T>({
  items,
  itemHeight,
  height,
  renderItem,
  overscan = 5,
  onScroll,
  onEndReached,
  endReachedThreshold = 200,
  loading = false,
  className,
  style
}: VirtualListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const isScrollingRef = useRef(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // 计算项目高度
  const getItemHeight = useCallback((index: number): number => {
    if (typeof itemHeight === 'function') {
      return itemHeight(index, items[index]);
    }
    return itemHeight;
  }, [itemHeight, items]);

  // 计算项目偏移量
  const getItemOffset = useCallback((index: number): number => {
    if (typeof itemHeight === 'function') {
      let offset = 0;
      for (let i = 0; i < index; i++) {
        offset += getItemHeight(i);
      }
      return offset;
    }
    return index * itemHeight;
  }, [itemHeight, getItemHeight]);

  // 计算总高度
  const totalHeight = useMemo(() => {
    if (typeof itemHeight === 'function') {
      return items.reduce((total, _, index) => total + getItemHeight(index), 0);
    }
    return items.length * itemHeight;
  }, [items.length, itemHeight, getItemHeight]);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    if (items.length === 0) {
      return { start: 0, end: 0 };
    }

    let start = 0;
    let end = items.length - 1;

    if (typeof itemHeight === 'function') {
      // 动态高度的情况
      let currentOffset = 0;
      for (let i = 0; i < items.length; i++) {
        const currentHeight = getItemHeight(i);
        if (currentOffset + currentHeight > scrollTop) {
          start = Math.max(0, i - overscan);
          break;
        }
        currentOffset += currentHeight;
      }

      currentOffset = getItemOffset(start);
      for (let i = start; i < items.length; i++) {
        if (currentOffset > scrollTop + height + overscan * getItemHeight(i)) {
          end = i;
          break;
        }
        currentOffset += getItemHeight(i);
      }
    } else {
      // 固定高度的情况
      start = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
      end = Math.min(
        items.length - 1,
        Math.ceil((scrollTop + height) / itemHeight) + overscan
      );
    }

    return { start, end };
  }, [scrollTop, height, items.length, itemHeight, overscan, getItemHeight, getItemOffset]);

  // 可见项目
  const visibleItems = useMemo(() => {
    const result = [];
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      if (items[i]) {
        result.push({
          index: i,
          item: items[i],
          offset: getItemOffset(i),
          height: getItemHeight(i)
        });
      }
    }
    return result;
  }, [visibleRange, items, getItemOffset, getItemHeight]);

  // 处理滚动
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);

    // 检查是否到达底部
    if (onEndReached) {
      const { scrollHeight, clientHeight } = e.currentTarget;
      if (scrollHeight - newScrollTop - clientHeight <= endReachedThreshold) {
        onEndReached();
      }
    }

    // 标记正在滚动
    isScrollingRef.current = true;
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }
    scrollTimeoutRef.current = setTimeout(() => {
      isScrollingRef.current = false;
    }, 150);
  }, [onScroll, onEndReached, endReachedThreshold]);

  // 滚动到指定项目
  const scrollToItem = useCallback((index: number, align: 'start' | 'center' | 'end' = 'start') => {
    if (!containerRef.current || index < 0 || index >= items.length) {
      return;
    }

    const itemOffset = getItemOffset(index);
    const itemHeight = getItemHeight(index);
    let scrollTop = itemOffset;

    if (align === 'center') {
      scrollTop = itemOffset - (height - itemHeight) / 2;
    } else if (align === 'end') {
      scrollTop = itemOffset - height + itemHeight;
    }

    scrollTop = Math.max(0, Math.min(scrollTop, totalHeight - height));
    containerRef.current.scrollTop = scrollTop;
  }, [items.length, getItemOffset, getItemHeight, height, totalHeight]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return (
    <VirtualContainer
      ref={containerRef}
      height={height}
      onScroll={handleScroll}
      className={className}
      style={style}
    >
      <VirtualContent totalHeight={totalHeight}>
        <VirtualViewport translateY={visibleRange.start > 0 ? getItemOffset(visibleRange.start) : 0}>
          {visibleItems.map(({ index, item, height: itemHeight }) => (
            <div
              key={index}
              style={{
                height: itemHeight,
                overflow: 'hidden'
              }}
            >
              {renderItem(item, index)}
            </div>
          ))}
          
          {loading && (
            <LoadingContainer>
              <Spin size="small" />
            </LoadingContainer>
          )}
        </VirtualViewport>
      </VirtualContent>
    </VirtualContainer>
  );
}

export default VirtualList;
