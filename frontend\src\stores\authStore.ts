import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginForm, RegisterForm } from '../types';
import { authService } from '../services/auth';
import { message } from 'antd';

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

interface AuthActions {
  login: (data: LoginForm) => Promise<boolean>;
  register: (data: RegisterForm) => Promise<boolean>;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
  updateUser: (user: User) => void;
  checkAuth: () => void;
  initializeAuth: () => void;
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // 状态
      user: null,
      token: null,
      isLoading: false,
      isAuthenticated: false,

      // 登录
      login: async (data: LoginForm) => {
        set({ isLoading: true });
        try {
          const response = await authService.login(data);
          if (response.success && response.data) {
            const { user, token } = response.data;
            authService.saveAuth(user, token);
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false
            });
            message.success('登录成功');
            return true;
          } else {
            message.error(response.message || '登录失败');
            set({ isLoading: false });
            return false;
          }
        } catch (error: any) {
          message.error(error.response?.data?.message || '登录失败');
          set({ isLoading: false });
          return false;
        }
      },

      // 注册
      register: async (data: RegisterForm) => {
        set({ isLoading: true });
        try {
          const response = await authService.register(data);
          if (response.success && response.data) {
            const { user, token } = response.data;
            authService.saveAuth(user, token);
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false
            });
            message.success('注册成功');
            return true;
          } else {
            message.error(response.message || '注册失败');
            set({ isLoading: false });
            return false;
          }
        } catch (error: any) {
          message.error(error.response?.data?.message || '注册失败');
          set({ isLoading: false });
          return false;
        }
      },

      // 登出
      logout: () => {
        authService.logout();
        set({
          user: null,
          token: null,
          isAuthenticated: false
        });
        message.success('已退出登录');
      },

      // 刷新token
      refreshToken: async () => {
        const currentToken = get().token;
        if (!currentToken) return false;

        try {
          const response = await authService.refreshToken(currentToken);
          if (response.success && response.data) {
            const { token } = response.data;
            localStorage.setItem('token', token);
            set({ token });
            return true;
          }
          return false;
        } catch (error) {
          // 刷新失败，清除认证信息
          get().logout();
          return false;
        }
      },

      // 更新用户信息
      updateUser: (user: User) => {
        localStorage.setItem('user', JSON.stringify(user));
        set({ user });
      },

      // 检查认证状态
      checkAuth: () => {
        const token = authService.getToken();
        const user = authService.getCurrentUser();

        if (token && user) {
          set({
            user,
            token,
            isAuthenticated: true
          });
        } else {
          set({
            user: null,
            token: null,
            isAuthenticated: false
          });
        }
      },

      // 初始化认证状态
      initializeAuth: () => {
        const { checkAuth } = get();
        checkAuth();
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
